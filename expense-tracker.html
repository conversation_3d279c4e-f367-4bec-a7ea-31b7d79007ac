<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出记录 - 生活NOTE</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js用于统计图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    }
                }
            }
        }
    </script>
    <style>
        .nav-link {
            color: #6B7280;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 0.5rem 0;
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .nav-link:hover { color: #F59E0B; }
        .nav-link.active { color: #F59E0B; font-weight: 600; }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #F59E0B;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .nav-link:hover::after,
        .nav-link.active::after { transform: scaleX(1); }
        .mobile-nav-link {
            color: #6B7280;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .mobile-nav-link:hover { color: #3B82F6; background-color: #F3F4F6; }
        .mobile-nav-link.active { color: #3B82F6; background-color: rgba(59, 130, 246, 0.1); font-weight: 600; }
        
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
        }
        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }
        .toast.success { background-color: #10B981; }
        .toast.error { background-color: #EF4444; }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-green-500 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-800">生活NOTE</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="fruit-plan.html" class="nav-link">
                        <i class="fa fa-apple mr-2"></i>水果计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link active">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-blue-500 focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="fruit-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md">
                    <i class="fa fa-apple mr-2"></i>水果计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link active block px-3 py-2 rounded-md">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8" style="padding-top: 2rem;">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fa fa-money text-yellow-500 mr-2"></i>支出记录
            </h1>
            <p class="text-gray-600">记录和管理您的日常支出</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧：添加新支出 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-plus-circle text-blue-600 mr-2"></i>添加新支出
                    </h2>
                    
                    <form id="expense-form" class="space-y-4">
                        <div>
                            <label for="expense-date" class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                            <input type="date" id="expense-date" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required>
                        </div>
                        
                        <div>
                            <label for="expense-category" class="block text-sm font-medium text-gray-700 mb-1">类别</label>
                            <select id="expense-category" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required>
                                <option value="">请选择类别</option>
                                <option value="餐饮">餐饮</option>
                                <option value="交通">交通</option>
                                <option value="购物">购物</option>
                                <option value="住房">住房</option>
                                <option value="娱乐">娱乐</option>
                                <option value="医疗">医疗</option>
                                <option value="教育">教育</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="expense-amount" class="block text-sm font-medium text-gray-700 mb-1">金额 (¥)</label>
                            <input type="number" id="expense-amount" min="0.01" step="0.01" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0.00" required>
                        </div>
                        
                        <div>
                            <label for="expense-note" class="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
                            <textarea id="expense-note" rows="2" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="添加备注信息..."></textarea>
                        </div>
                        
                        <button type="submit" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fa fa-save mr-2"></i>保存记录
                        </button>
                    </form>
                </div>
                
                <!-- 本月概览 -->
                <div class="bg-white rounded-xl p-6 shadow-md mt-6">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-bar-chart text-yellow-500 mr-2"></i>本月概览
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">总支出</span>
                            <span id="monthly-total" class="font-bold text-lg text-red-600">¥0.00</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">记录数量</span>
                            <span id="monthly-count" class="font-bold text-lg text-blue-600">0 条</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">平均每日</span>
                            <span id="daily-average" class="font-bold text-lg text-green-600">¥0.00</span>
                        </div>
                        
                        <div class="pt-2 border-t border-gray-200">
                            <span class="text-sm text-gray-500 block mb-1">主要支出类别</span>
                            <div id="top-category" class="flex items-center">
                                <span class="text-gray-400">暂无数据</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：支出列表 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl p-6 shadow-md">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
                        <h2 class="text-xl font-bold flex items-center">
                            <i class="fa fa-list text-green-500 mr-2"></i>支出记录
                        </h2>
                        <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                            <input type="text" id="search-expenses" placeholder="搜索支出记录..." 
                                class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex-1 sm:w-48">
                            <button onclick="exportData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap">
                                <i class="fa fa-download mr-1"></i>导出数据
                            </button>
                        </div>
                    </div>
                    
                    <div id="expenses-list" class="space-y-3 min-h-[300px]">
                        <!-- 支出记录将在这里显示 -->
                    </div>
                    
                    <!-- 分页控制 -->
                    <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                        <div class="text-sm text-gray-500">
                            显示第 <span id="page-info">1-5</span> 条，共 <span id="total-count">0</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button id="prev-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fa fa-chevron-left mr-1"></i>上一页
                            </button>
                            <button id="next-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
                                下一页<i class="fa fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计图表区域 -->
        <div class="mt-8">
            <div class="bg-white rounded-xl p-6 shadow-md">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                    <h2 class="text-xl font-bold flex items-center">
                        <i class="fa fa-pie-chart text-blue-500 mr-2"></i>支出统计
                    </h2>
                    
                    <div class="flex space-x-2 mt-2 sm:mt-0">
                        <button id="weekly-btn" class="px-4 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium transition-all">
                            本周
                        </button>
                        <button id="monthly-btn" class="px-4 py-1.5 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium transition-all">
                            本月
                        </button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">支出分布</h3>
                        <div class="aspect-square max-w-xs mx-auto">
                            <canvas id="expense-chart"></canvas>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 mb-3">支出趋势</h3>
                        <div class="aspect-square max-w-xs mx-auto">
                            <canvas id="trend-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        class ExpenseManager {
            constructor() {
                this.expenses = JSON.parse(localStorage.getItem('expenses')) || [];
                this.currentPage = 1;
                this.itemsPerPage = 5;
                this.currentView = 'weekly';
                this.init();
            }

            init() {
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();
                this.setupEventListeners();
                
                // 设置默认日期为今天
                const dateInput = document.getElementById('expense-date');
                if (dateInput) {
                    dateInput.valueAsDate = new Date();
                }
                
                console.log('ExpenseManager 初始化完成，当前支出记录数量:', this.expenses.length);
            }

            setupEventListeners() {
                // 表单提交事件
                const form = document.getElementById('expense-form');
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.addExpense();
                    });
                }

                // 搜索功能
                const searchInput = document.getElementById('search-expenses');
                if (searchInput) {
                    searchInput.addEventListener('input', () => {
                        this.currentPage = 1;
                        this.renderExpenses();
                    });
                }

                // 统计视图切换
                this.setupViewToggle();

                // 分页控制
                this.setupPagination();
            }

            setupViewToggle() {
                const weeklyBtn = document.getElementById('weekly-btn');
                const monthlyBtn = document.getElementById('monthly-btn');

                if (weeklyBtn) {
                    weeklyBtn.addEventListener('click', () => {
                        this.currentView = 'weekly';
                        weeklyBtn.classList.add('bg-blue-600', 'text-white');
                        weeklyBtn.classList.remove('bg-gray-200', 'hover:bg-gray-300');
                        monthlyBtn.classList.add('bg-gray-200', 'hover:bg-gray-300');
                        monthlyBtn.classList.remove('bg-blue-600', 'text-white');
                        this.updateCharts();
                    });
                }

                if (monthlyBtn) {
                    monthlyBtn.addEventListener('click', () => {
                        this.currentView = 'monthly';
                        monthlyBtn.classList.add('bg-blue-600', 'text-white');
                        monthlyBtn.classList.remove('bg-gray-200', 'hover:bg-gray-300');
                        weeklyBtn.classList.add('bg-gray-200', 'hover:bg-gray-300');
                        weeklyBtn.classList.remove('bg-blue-600', 'text-white');
                        this.updateCharts();
                    });
                }
            }

            setupPagination() {
                const prevBtn = document.getElementById('prev-page');
                const nextBtn = document.getElementById('next-page');

                if (prevBtn) {
                    prevBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.renderExpenses();
                        }
                    });
                }

                if (nextBtn) {
                    nextBtn.addEventListener('click', () => {
                        const filteredExpenses = this.getFilteredExpenses();
                        const totalPages = Math.ceil(filteredExpenses.length / this.itemsPerPage);
                        if (this.currentPage < totalPages) {
                            this.currentPage++;
                            this.renderExpenses();
                        }
                    });
                }
            }

            addExpense() {
                const date = document.getElementById('expense-date').value;
                const category = document.getElementById('expense-category').value;
                const amount = parseFloat(document.getElementById('expense-amount').value);
                const note = document.getElementById('expense-note').value;

                console.log('添加支出:', { date, category, amount, note });

                if (!date || !category || !amount || amount <= 0) {
                    this.showToast('请填写完整的支出信息', 'error');
                    return;
                }

                const newExpense = {
                    id: Date.now().toString(),
                    date,
                    category,
                    amount,
                    note: note || ''
                };

                this.expenses.unshift(newExpense);
                this.saveExpenses();

                // 重置表单
                document.getElementById('expense-form').reset();
                document.getElementById('expense-date').valueAsDate = new Date();

                // 更新UI
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();

                this.showToast('支出记录已添加', 'success');
                console.log('支出添加成功，当前总数:', this.expenses.length);
            }

            getFilteredExpenses() {
                const searchTerm = document.getElementById('search-expenses')?.value.toLowerCase() || '';

                if (!searchTerm) {
                    return this.expenses;
                }

                return this.expenses.filter(expense =>
                    expense.category.toLowerCase().includes(searchTerm) ||
                    expense.note.toLowerCase().includes(searchTerm) ||
                    expense.amount.toString().includes(searchTerm)
                );
            }

            renderExpenses() {
                const container = document.getElementById('expenses-list');
                if (!container) return;

                const filteredExpenses = this.getFilteredExpenses();
                const totalPages = Math.ceil(filteredExpenses.length / this.itemsPerPage);
                const startIndex = (this.currentPage - 1) * this.itemsPerPage;
                const endIndex = startIndex + this.itemsPerPage;
                const currentExpenses = filteredExpenses.slice(startIndex, endIndex);

                // 更新分页信息
                this.updatePaginationInfo(filteredExpenses, startIndex, endIndex, totalPages);

                if (filteredExpenses.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">暂无支出记录</p>';
                    return;
                }

                const html = currentExpenses.map(expense => `
                    <div class="flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="flex-1">
                            <div class="flex items-center mb-1">
                                <span class="font-medium text-gray-800">${expense.category}</span>
                                <span class="text-sm text-gray-500 ml-2">${expense.date}</span>
                            </div>
                            ${expense.note ? `<p class="text-sm text-gray-600">${expense.note}</p>` : ''}
                        </div>
                        <div class="text-right">
                            <span class="font-bold text-red-600">¥${expense.amount.toFixed(2)}</span>
                            <button onclick="expenseManager.deleteExpense('${expense.id}')"
                                class="ml-2 text-red-500 hover:text-red-700 transition-colors">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            updatePaginationInfo(filteredExpenses, startIndex, endIndex, totalPages) {
                const pageInfo = document.getElementById('page-info');
                const totalCount = document.getElementById('total-count');
                const prevBtn = document.getElementById('prev-page');
                const nextBtn = document.getElementById('next-page');

                if (pageInfo) {
                    const start = filteredExpenses.length > 0 ? startIndex + 1 : 0;
                    const end = Math.min(endIndex, filteredExpenses.length);
                    pageInfo.textContent = `${start}-${end}`;
                }

                if (totalCount) {
                    totalCount.textContent = filteredExpenses.length.toString();
                }

                if (prevBtn) {
                    prevBtn.disabled = this.currentPage <= 1;
                }

                if (nextBtn) {
                    nextBtn.disabled = this.currentPage >= totalPages;
                }
            }

            deleteExpense(id) {
                if (confirm('确定要删除这条支出记录吗？')) {
                    this.expenses = this.expenses.filter(expense => expense.id !== id);
                    this.saveExpenses();
                    this.renderExpenses();
                    this.updateCharts();
                    this.updateSummary();
                    this.showToast('支出记录已删除', 'success');
                }
            }

            saveExpenses() {
                localStorage.setItem('expenses', JSON.stringify(this.expenses));
            }

            updateSummary() {
                const now = new Date();
                const currentMonth = now.getMonth();
                const currentYear = now.getFullYear();

                const monthlyExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
                });

                const total = monthlyExpenses.reduce((sum, expense) => sum + expense.amount, 0);
                const count = monthlyExpenses.length;
                const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
                const currentDay = now.getDate();
                const average = count > 0 ? total / Math.min(currentDay, daysInMonth) : 0;

                document.getElementById('monthly-total').textContent = `¥${total.toFixed(2)}`;
                document.getElementById('monthly-count').textContent = `${count} 条`;
                document.getElementById('daily-average').textContent = `¥${average.toFixed(2)}`;

                // 更新主要支出类别
                this.updateTopCategory(monthlyExpenses);
            }

            updateTopCategory(monthlyExpenses) {
                const categoryMap = {};
                monthlyExpenses.forEach(expense => {
                    if (!categoryMap[expense.category]) {
                        categoryMap[expense.category] = 0;
                    }
                    categoryMap[expense.category] += expense.amount;
                });

                const topCategoryEl = document.getElementById('top-category');
                if (Object.keys(categoryMap).length > 0) {
                    let topCategory = Object.keys(categoryMap)[0];
                    let maxAmount = categoryMap[topCategory];

                    for (let category in categoryMap) {
                        if (categoryMap[category] > maxAmount) {
                            topCategory = category;
                            maxAmount = categoryMap[category];
                        }
                    }

                    const percentage = monthlyExpenses.length > 0 ?
                        ((maxAmount / monthlyExpenses.reduce((sum, e) => sum + e.amount, 0)) * 100).toFixed(1) : 0;

                    topCategoryEl.innerHTML = `
                        <span class="font-medium text-gray-800">${topCategory}</span>
                        <span class="text-sm text-gray-500 ml-2">${percentage}%</span>
                    `;
                } else {
                    topCategoryEl.innerHTML = '<span class="text-gray-400">暂无数据</span>';
                }
            }

            updateCharts() {
                const { categoryData, trendData, labels } = this.getCurrentViewData();
                this.updateExpenseChart(categoryData);
                this.updateTrendChart(trendData, labels);
            }

            getCurrentViewData() {
                const now = new Date();
                let startDate, endDate, labels = [];

                if (this.currentView === 'weekly') {
                    // 本周数据
                    const dayOfWeek = now.getDay() || 7;
                    startDate = new Date(now);
                    startDate.setDate(now.getDate() - (dayOfWeek - 1));
                    startDate.setHours(0, 0, 0, 0);

                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    endDate.setHours(23, 59, 59, 999);

                    labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                } else {
                    // 本月数据
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

                    const daysInMonth = endDate.getDate();
                    for (let i = 1; i <= daysInMonth; i++) {
                        labels.push(i.toString());
                    }
                }

                // 筛选时间范围内的支出
                const filteredExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate >= startDate && expenseDate <= endDate;
                });

                // 计算类别数据
                const categoryMap = {};
                filteredExpenses.forEach(expense => {
                    if (!categoryMap[expense.category]) {
                        categoryMap[expense.category] = 0;
                    }
                    categoryMap[expense.category] += expense.amount;
                });

                const categoryData = {
                    labels: Object.keys(categoryMap),
                    data: Object.values(categoryMap)
                };

                // 计算趋势数据
                const trendData = new Array(labels.length).fill(0);

                filteredExpenses.forEach(expense => {
                    const expenseDate = new Date(expense.date);
                    let index;

                    if (this.currentView === 'weekly') {
                        let dayIndex = expenseDate.getDay() || 7;
                        index = dayIndex - 1;
                    } else {
                        index = expenseDate.getDate() - 1;
                    }

                    if (index >= 0 && index < trendData.length) {
                        trendData[index] += expense.amount;
                    }
                });

                return { categoryData, trendData, labels };
            }

            updateExpenseChart(categoryData) {
                const ctx = document.getElementById('expense-chart').getContext('2d');

                if (window.expenseChart) {
                    window.expenseChart.destroy();
                }

                if (categoryData.labels.length === 0) {
                    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#9CA3AF';
                    ctx.textAlign = 'center';
                    ctx.fillText('暂无数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
                    return;
                }

                const colors = [
                    '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
                    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
                ];

                window.expenseChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryData.labels,
                        datasets: [{
                            data: categoryData.data,
                            backgroundColor: colors.slice(0, categoryData.labels.length),
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            updateTrendChart(trendData, labels) {
                const ctx = document.getElementById('trend-chart').getContext('2d');

                if (window.trendChart) {
                    window.trendChart.destroy();
                }

                window.trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '支出金额',
                            data: trendData,
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + value.toFixed(0);
                                    }
                                }
                            }
                        }
                    }
                });
            }

            showToast(message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;
                document.body.appendChild(toast);

                setTimeout(() => toast.classList.add('show'), 100);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);
            }
        }

        // 导出数据功能
        function exportData() {
            const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
            const dataStr = JSON.stringify(expenses, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `支出记录_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 全局变量
        let expenseManager;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');

            // 初始化支出管理器
            expenseManager = new ExpenseManager();

            // 移动端菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // 滚动时导航栏样式变化
            window.addEventListener('scroll', function() {
                const nav = document.querySelector('nav');
                if (nav) {
                    if (window.scrollY > 10) {
                        nav.classList.add('shadow-lg');
                    } else {
                        nav.classList.remove('shadow-lg');
                    }
                }
            });

            console.log('支出记录页面初始化完成');
        });
    </script>
</body>
</html>
