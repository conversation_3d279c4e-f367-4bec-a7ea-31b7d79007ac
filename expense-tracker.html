<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出记录 - 生活NOTE</title>
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="common-styles.css">
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入通用工具 -->
    <script src="common-utils.js"></script>
    
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        dark: '#1F2937',
                        light: '#F3F4F6',
                        danger: '#EF4444',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            }
            .nav-active {
                @apply text-primary border-b-2 border-primary;
            }
            .nav-highlight {
                @apply relative h-16 flex items-center transition-all duration-200;
            }
            .input-focus {
                @apply focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-dark">
    <!-- 引入统一导航栏 -->
    <div id="navbar-container"></div>

    <main class="container mx-auto px-4 py-8" style="padding-top: 5rem;">
        <!-- 页面标题 -->
        <div class="mb-8 text-center">
            <h1 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-2">支出记录</h1>
            <p class="text-gray-500">轻松记录和管理您的日常支出，掌握财务状况</p>
        </div>
        
        <!-- 主要内容区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧：添加新支出 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl p-6 card-shadow transition-all duration-300 hover:shadow-lg">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-plus-circle text-primary mr-2"></i>添加新支出
                    </h2>
                    
                    <form id="expense-form" class="space-y-4">
                        <div>
                            <label for="expense-date" class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                            <input type="date" id="expense-date" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition-all"
                                required>
                        </div>
                        
                        <div>
                            <label for="expense-category" class="block text-sm font-medium text-gray-700 mb-1">类别</label>
                            <select id="expense-category" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition-all"
                                required>
                                <option value="">请选择类别</option>
                                <option value="food">餐饮</option>
                                <option value="transport">交通</option>
                                <option value="shopping">购物</option>
                                <option value="housing">住房</option>
                                <option value="entertainment">娱乐</option>
                                <option value="healthcare">医疗</option>
                                <option value="education">教育</option>
                        
                                <option value="other">其他</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="expense-amount" class="block text-sm font-medium text-gray-700 mb-1">金额 (¥)</label>
                            <input type="number" id="expense-amount" min="0.01" step="0.01" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition-all"
                                placeholder="0.00" required>
                        </div>
                        
                        <div>
                            <label for="expense-note" class="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
                            <textarea id="expense-note" rows="2" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition-all"
                                placeholder="添加备注信息..."></textarea>
                        </div>
                        
                        <button type="submit" 
                            class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fa fa-save mr-2"></i>保存记录
                        </button>
                    </form>
                </div>
                
                <!-- 本月概览 -->
                <div class="bg-white rounded-xl p-6 card-shadow mt-6 transition-all duration-300 hover:shadow-lg">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-bar-chart text-accent mr-2"></i>本月概览
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">总支出</span>
                            <span id="monthly-total" class="font-bold text-lg">¥0.00</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">平均每日</span>
                            <span id="daily-average" class="font-medium">¥0.00</span>
                        </div>
                        
                        <div class="pt-2">
                            <span class="text-sm text-gray-500 block mb-1">主要支出类别</span>
                            <div id="top-category" class="flex items-center">
                                <span class="inline-block w-3 h-3 rounded-full bg-primary mr-2"></span>
                                <span>暂无数据</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：记录列表和统计 -->
            <div class="lg:col-span-2">
                <!-- 统计图表 -->
                <div class="bg-white rounded-xl p-6 card-shadow transition-all duration-300 hover:shadow-lg mb-6">
                    <div class="flex flex-wrap justify-between items-center mb-6">
                        <h2 class="text-xl font-bold flex items-center">
                            <i class="fa fa-pie-chart text-secondary mr-2"></i>支出统计
                        </h2>
                        
                        <div class="flex space-x-2 mt-2 sm:mt-0">
                            <button id="weekly-btn" class="px-4 py-1.5 bg-primary text-white rounded-md text-sm font-medium transition-all">
                                本周
                            </button>
                            <button id="monthly-btn" class="px-4 py-1.5 bg-gray-200 hover:bg-gray-300 rounded-md text-sm font-medium transition-all">
                                本月
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-3">支出分布</h3>
                            <div class="aspect-square max-w-xs mx-auto">
                                <canvas id="expense-chart"></canvas>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-3">支出趋势</h3>
                            <div class="aspect-auto h-64">
                                <canvas id="trend-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 支出记录列表 -->
                <div class="bg-white rounded-xl p-6 card-shadow transition-all duration-300 hover:shadow-lg">
                    <div class="flex flex-wrap justify-between items-center mb-6">
                        <h2 class="text-xl font-bold flex items-center">
                            <i class="fa fa-history text-dark mr-2"></i>支出记录
                        </h2>
                        
                        <div class="relative mt-2 sm:mt-0">
                            <input type="text" id="search-expenses" 
                                class="pl-9 pr-4 py-2 border border-gray-300 rounded-lg input-focus transition-all w-full sm:w-auto"
                                placeholder="搜索记录...">
                            <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">日期</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                                    <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                    <th class="px-4 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tr-lg">操作</th>
                                </tr>
                            </thead>
                            <tbody id="expenses-list" class="bg-white divide-y divide-gray-200">
                                <!-- 记录将通过JavaScript动态添加 -->
                                <tr class="text-center">
                                    <td colspan="5" class="px-4 py-8 text-gray-500">
                                        <i class="fa fa-file-text-o text-3xl mb-2 block opacity-30"></i>
                                        暂无支出记录
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div id="pagination" class="mt-6 flex justify-between items-center hidden">
                        <button id="prev-page" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fa fa-chevron-left mr-1"></i> 上一页
                        </button>
                        
                        <div class="text-sm text-gray-500">
                            第 <span id="current-page">1</span> 页，共 <span id="total-pages">0</span> 页
                        </div>
                        
                        <button id="next-page" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                            下一页 <i class="fa fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center text-gray-500 text-sm">
                <p>© 2023 生活NOTE. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- 删除确认模态框 -->
    <div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 transform transition-all scale-100">
            <h3 class="text-lg font-bold text-gray-900 mb-2">确认删除</h3>
            <p class="text-gray-600 mb-4">您确定要删除这条支出记录吗？此操作无法撤销。</p>
            
            <div class="flex justify-end space-x-3">
                <button id="cancel-delete" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all">
                    取消
                </button>
                <button id="confirm-delete" class="px-4 py-2 bg-danger text-white rounded-md text-sm font-medium hover:bg-danger/90 transition-all">
                    删除
                </button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="toast" class="fixed bottom-6 right-6 bg-dark text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center z-50">
        <i id="toast-icon" class="fa fa-check-circle mr-2"></i>
        <span id="toast-message"></span>
    </div>

    <script>
        // 初始化日期为今天
        document.getElementById('expense-date').valueAsDate = new Date();
        
        // 移动端菜单切换
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // 数据管理
        class ExpenseManager {
            constructor() {
                this.expenses = JSON.parse(localStorage.getItem('expenses')) || [];
                this.currentPage = 1;
                this.itemsPerPage = 5;
                this.currentView = 'weekly'; // 默认显示周统计
                this.init();
            }
            
            // 初始化
            init() {
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();
                
                // 表单提交事件
                document.getElementById('expense-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.addExpense();
                });
                
                // 搜索功能
                document.getElementById('search-expenses').addEventListener('input', () => {
                    this.currentPage = 1;
                    this.renderExpenses();
                });
                
                // 统计视图切换
                document.getElementById('weekly-btn').addEventListener('click', () => {
                    this.currentView = 'weekly';
                    document.getElementById('weekly-btn').classList.add('bg-primary', 'text-white');
                    document.getElementById('weekly-btn').classList.remove('bg-gray-200', 'hover:bg-gray-300');
                    document.getElementById('monthly-btn').classList.add('bg-gray-200', 'hover:bg-gray-300');
                    document.getElementById('monthly-btn').classList.remove('bg-primary', 'text-white');
                    this.updateCharts();
                });
                
                document.getElementById('monthly-btn').addEventListener('click', () => {
                    this.currentView = 'monthly';
                    document.getElementById('monthly-btn').classList.add('bg-primary', 'text-white');
                    document.getElementById('monthly-btn').classList.remove('bg-gray-200', 'hover:bg-gray-300');
                    document.getElementById('weekly-btn').classList.add('bg-gray-200', 'hover:bg-gray-300');
                    document.getElementById('weekly-btn').classList.remove('bg-primary', 'text-white');
                    this.updateCharts();
                });
                
                // 分页控制
                document.getElementById('prev-page').addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.renderExpenses();
                    }
                });
                
                document.getElementById('next-page').addEventListener('click', () => {
                    const totalPages = this.getTotalPages();
                    if (this.currentPage < totalPages) {
                        this.currentPage++;
                        this.renderExpenses();
                    }
                });
                
                // 删除确认
                document.getElementById('cancel-delete').addEventListener('click', () => {
                    this.hideDeleteModal();
                });
                
                document.getElementById('confirm-delete').addEventListener('click', () => {
                    this.performDelete();
                });
            }
            
            // 添加新支出
            addExpense() {
                const date = document.getElementById('expense-date').value;
                const category = document.getElementById('expense-category').value;
                const amount = parseFloat(document.getElementById('expense-amount').value);
                const note = document.getElementById('expense-note').value;
                
                if (!date || !category || !amount) return;
                
                const newExpense = {
                    id: Date.now().toString(),
                    date,
                    category,
                    amount,
                    note
                };
                
                this.expenses.unshift(newExpense); // 添加到开头
                this.saveExpenses();
                
                // 重置表单
                document.getElementById('expense-form').reset();
                document.getElementById('expense-date').valueAsDate = new Date();
                
                // 更新UI
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();
                
                // 显示提示
                this.showToast('支出记录已添加', 'success');
            }
            
            // 获取类别名称
            getCategoryName(category) {
                const categories = {
                    'food': '餐饮',
                    'transport': '交通',
                    'shopping': '购物',
                    'housing': '住房',
                    'entertainment': '娱乐',
                    'healthcare': '医疗',
                    'education': '教育',
                    'other': '其他'
                };
                return categories[category] || category;
            }
            
            // 获取类别颜色
            getCategoryColor(category) {
                const colors = {
                    'food': '#3B82F6', // 蓝色
                    'transport': '#10B981', // 绿色
                    'shopping': '#F59E0B', // 黄色
                    'housing': '#8B5CF6', // 紫色
                    'entertainment': '#EC4899', // 粉色
                    'healthcare': '#6366F1', // 靛蓝色
                    'education': '#F97316', // 橙色
                    'other': '#6B7280' // 灰色
                };
                return colors[category] || '#6B7280';
            }
            
            // 获取类别图标
            getCategoryIcon(category) {
                const icons = {
                    'food': 'fa-cutlery',
                    'transport': 'fa-car',
                    'shopping': 'fa-shopping-bag',
                    'housing': 'fa-home',
                    'entertainment': 'fa-film',
                    'healthcare': 'fa-medkit',
                    'education': 'fa-book',
                    'other': 'fa-ellipsis-h'
                };
                return icons[category] || 'fa-ellipsis-h';
            }
            
            // 渲染支出记录
            renderExpenses() {
                const listContainer = document.getElementById('expenses-list');
                const searchTerm = document.getElementById('search-expenses').value.toLowerCase();
                
                // 筛选记录
                let filteredExpenses = this.expenses.filter(expense => {
                    const matchesSearch = 
                        this.getCategoryName(expense.category).toLowerCase().includes(searchTerm) ||
                        expense.note.toLowerCase().includes(searchTerm) ||
                        expense.amount.toString().includes(searchTerm) ||
                        expense.date.includes(searchTerm);
                    return matchesSearch;
                });
                
                // 分页
                const totalPages = this.getTotalPages(filteredExpenses);
                const startIndex = (this.currentPage - 1) * this.itemsPerPage;
                const paginatedExpenses = filteredExpenses.slice(startIndex, startIndex + this.itemsPerPage);
                
                // 更新分页信息
                document.getElementById('current-page').textContent = this.currentPage;
                document.getElementById('total-pages').textContent = totalPages;
                document.getElementById('prev-page').disabled = this.currentPage <= 1;
                document.getElementById('next-page').disabled = this.currentPage >= totalPages;
                
                // 显示或隐藏分页控件
                if (totalPages <= 1) {
                    document.getElementById('pagination').classList.add('hidden');
                } else {
                    document.getElementById('pagination').classList.remove('hidden');
                }
                
                // 清空列表
                listContainer.innerHTML = '';
                
                // 没有记录时显示提示
                if (paginatedExpenses.length === 0) {
                    listContainer.innerHTML = `
                        <tr class="text-center">
                            <td colspan="5" class="px-4 py-8 text-gray-500">
                                <i class="fa fa-search text-3xl mb-2 block opacity-30"></i>
                                没有找到匹配的记录
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                // 添加记录到列表
                paginatedExpenses.forEach(expense => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50 transition-colors';
                    
                    // 格式化日期
                    const formattedDate = new Date(expense.date).toLocaleDateString('zh-CN');
                    
                    row.innerHTML = `
                        <td class="px-4 py-4 whitespace-nowrap">${formattedDate}</td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="flex items-center">
                                <i class="fa ${this.getCategoryIcon(expense.category)} text-xs mr-2" style="color: ${this.getCategoryColor(expense.category)}"></i>
                                ${this.getCategoryName(expense.category)}
                            </span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap font-medium">¥${expense.amount.toFixed(2)}</td>
                        <td class="px-4 py-4">${expense.note || '-'}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button class="text-danger hover:text-danger/80 transition-colors delete-btn" data-id="${expense.id}">
                                <i class="fa fa-trash-o"></i> 删除
                            </button>
                        </td>
                    `;
                    
                    listContainer.appendChild(row);
                });
                
                // 添加删除事件监听
                document.querySelectorAll('.delete-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.showDeleteModal(e.target.closest('.delete-btn').dataset.id);
                    });
                });
            }
            
            // 获取总页数
            getTotalPages(filteredExpenses = null) {
                const expensesToCount = filteredExpenses || this.expenses;
                return Math.ceil(expensesToCount.length / this.itemsPerPage);
            }
            
            // 显示删除确认模态框
            showDeleteModal(id) {
                this.deleteId = id;
                document.getElementById('delete-modal').classList.remove('hidden');
                
                // 添加动画效果
                setTimeout(() => {
                    const modalContent = document.querySelector('#delete-modal > div');
                    modalContent.classList.add('scale-100');
                    modalContent.classList.remove('scale-95');
                }, 10);
            }
            
            // 隐藏删除确认模态框
            hideDeleteModal() {
                const modalContent = document.querySelector('#delete-modal > div');
                modalContent.classList.add('scale-95');
                modalContent.classList.remove('scale-100');
                
                setTimeout(() => {
                    document.getElementById('delete-modal').classList.add('hidden');
                    this.deleteId = null;
                }, 200);
            }
            
            // 执行删除操作
            performDelete() {
                if (!this.deleteId) return;
                
                this.expenses = this.expenses.filter(expense => expense.id !== this.deleteId);
                this.saveExpenses();
                
                this.hideDeleteModal();
                this.renderExpenses();
                this.updateCharts();
                this.updateSummary();
                
                this.showToast('支出记录已删除', 'success');
            }
            
            // 保存数据到本地存储
            saveExpenses() {
                localStorage.setItem('expenses', JSON.stringify(this.expenses));
            }
            
            // 更新统计图表
            updateCharts() {
                // 获取当前视图的数据（周或月）
                const { categoryData, trendData, labels } = this.getCurrentViewData();
                
                // 支出分布图表
                const expenseChartCtx = document.getElementById('expense-chart').getContext('2d');
                
                // 销毁现有图表（如果存在）
                if (window.expenseChart) {
                    window.expenseChart.destroy();
                }
                
                window.expenseChart = new Chart(expenseChartCtx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryData.labels,
                        datasets: [{
                            data: categoryData.values,
                            backgroundColor: categoryData.colors,
                            borderWidth: 0,
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 15,
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.raw;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${context.label}: ¥${value.toFixed(2)} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        cutout: '70%',
                        animation: {
                            animateScale: true,
                            animateRotate: true
                        }
                    }
                });
                
                // 支出趋势图表
                const trendChartCtx = document.getElementById('trend-chart').getContext('2d');
                
                // 销毁现有图表（如果存在）
                if (window.trendChart) {
                    window.trendChart.destroy();
                }
                
                window.trendChart = new Chart(trendChartCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '支出金额',
                            data: trendData,
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderRadius: 6,
                            barThickness: 10,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `¥${context.raw.toFixed(2)}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + value;
                                    }
                                },
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        animation: {
                            duration: 1000
                        }
                    }
                });
            }
            
            // 获取当前视图（周/月）的数据
            getCurrentViewData() {
                const now = new Date();
                let startDate, endDate, labels = [];
                
                if (this.currentView === 'weekly') {
                    // 本周数据
                    const dayOfWeek = now.getDay() || 7; // 转换为周一为一周的第一天
                    startDate = new Date(now);
                    startDate.setDate(now.getDate() - (dayOfWeek - 1));
                    startDate.setHours(0, 0, 0, 0);
                    
                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    endDate.setHours(23, 59, 59, 999);
                    
                    // 生成周一至周日的标签
                    const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    labels = weekdays;
                } else {
                    // 本月数据
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    startDate.setHours(0, 0, 0, 0);
                    
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    endDate.setHours(23, 59, 59, 999);
                    
                    // 生成日期标签
                    const daysInMonth = endDate.getDate();
                    for (let i = 1; i <= daysInMonth; i++) {
                        labels.push(i + '日');
                    }
                }
                
                // 筛选出日期范围内的支出
                const filteredExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate >= startDate && expenseDate <= endDate;
                });
                
                // 按类别统计
                const categoryMap = {};
                filteredExpenses.forEach(expense => {
                    const categoryName = this.getCategoryName(expense.category);
                    if (!categoryMap[categoryName]) {
                        categoryMap[categoryName] = {
                            value: 0,
                            color: this.getCategoryColor(expense.category)
                        };
                    }
                    categoryMap[categoryName].value += expense.amount;
                });
                
                const categoryData = {
                    labels: Object.keys(categoryMap),
                    values: Object.values(categoryMap).map(item => item.value),
                    colors: Object.values(categoryMap).map(item => item.color)
                };
                
                // 按日期统计趋势
                const trendData = Array(labels.length).fill(0);
                
                filteredExpenses.forEach(expense => {
                    const expenseDate = new Date(expense.date);
                    let index;
                    
                    if (this.currentView === 'weekly') {
                        // 计算是星期几（0=周一，6=周日）
                        let dayIndex = expenseDate.getDay() || 7; // 转换为周一为0
                        index = dayIndex - 1;
                    } else {
                        // 计算是当月的第几天
                        index = expenseDate.getDate() - 1;
                    }
                    
                    if (index >= 0 && index < trendData.length) {
                        trendData[index] += expense.amount;
                    }
                });
                
                return { categoryData, trendData, labels };
            }
            
            // 更新本月概览
            updateSummary() {
                const now = new Date();
                const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                
                // 筛选本月支出
                const monthlyExpenses = this.expenses.filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate >= startOfMonth && expenseDate <= endOfMonth;
                });
                
                // 计算总支出
                const total = monthlyExpenses.reduce((sum, expense) => sum + expense.amount, 0);
                document.getElementById('monthly-total').textContent = `¥${total.toFixed(2)}`;
                
                // 计算平均每日支出
                const today = new Date();
                const dayOfMonth = Math.min(today.getDate(), endOfMonth.getDate());
                const dailyAverage = dayOfMonth > 0 ? total / dayOfMonth : 0;
                document.getElementById('daily-average').textContent = `¥${dailyAverage.toFixed(2)}`;
                
                // 找出主要支出类别
                const categoryMap = {};
                monthlyExpenses.forEach(expense => {
                    if (!categoryMap[expense.category]) {
                        categoryMap[expense.category] = 0;
                    }
                    categoryMap[expense.category] += expense.amount;
                });
                
                if (Object.keys(categoryMap).length > 0) {
                    let topCategory = Object.keys(categoryMap)[0];
                    let maxAmount = categoryMap[topCategory];
                    
                    for (const category in categoryMap) {
                        if (categoryMap[category] > maxAmount) {
                            maxAmount = categoryMap[category];
                            topCategory = category;
                        }
                    }
                    
                    document.getElementById('top-category').innerHTML = `
                        <span class="inline-block w-3 h-3 rounded-full" style="background-color: ${this.getCategoryColor(topCategory)}"></span>
                        <span>${this.getCategoryName(topCategory)} (¥${maxAmount.toFixed(2)})</span>
                    `;
                } else {
                    document.getElementById('top-category').innerHTML = `
                        <span class="inline-block w-3 h-3 rounded-full bg-gray-400 mr-2"></span>
                        <span>暂无数据</span>
                    `;
                }
            }
            
            // 显示提示消息
            showToast(message, type = 'info') {
                const toast = document.getElementById('toast');
                const toastMessage = document.getElementById('toast-message');
                const toastIcon = document.getElementById('toast-icon');
                
                toastMessage.textContent = message;
                
                // 设置图标
                if (type === 'success') {
                    toastIcon.className = 'fa fa-check-circle mr-2';
                    toast.classList.remove('bg-danger');
                    toast.classList.add('bg-primary');
                } else if (type === 'error') {
                    toastIcon.className = 'fa fa-exclamation-circle mr-2';
                    toast.classList.remove('bg-primary');
                    toast.classList.add('bg-danger');
                } else {
                    toastIcon.className = 'fa fa-info-circle mr-2';
                    toast.classList.remove('bg-danger');
                    toast.classList.add('bg-primary');
                }
                
                // 显示提示
                toast.classList.remove('translate-y-20', 'opacity-0');
                toast.classList.add('translate-y-0', 'opacity-100');
                
                // 3秒后隐藏
                setTimeout(() => {
                    toast.classList.remove('translate-y-0', 'opacity-100');
                    toast.classList.add('translate-y-20', 'opacity-0');
                }, 3000);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            const expenseManager = new ExpenseManager();
            
            // 滚动时改变导航栏样式
            window.addEventListener('scroll', () => {
                const header = document.querySelector('header');
                if (window.scrollY > 10) {
                    header.classList.add('shadow');
                    header.classList.remove('shadow-sm');
                } else {
                    header.classList.remove('shadow');
                    header.classList.add('shadow-sm');
                }
            });
        });
    </script>

    <!-- 加载统一导航栏 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载导航栏
            fetch('navbar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('navbar-container').innerHTML = html;

                    // 执行导航栏中的脚本
                    const scripts = document.getElementById('navbar-container').querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                    });
                })
                .catch(error => {
                    console.error('加载导航栏失败:', error);
                });
        });
    </script>
</body>
</html>
