/* 通用工具函数 - 生活NOTE */

// 通知系统
class NotificationSystem {
    constructor() {
        this.createNotificationContainer();
    }

    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                bottom: 1rem;
                right: 1rem;
                z-index: 1000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    show(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} show`;
        notification.textContent = message;
        notification.style.cssText = `
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: auto;
            cursor: pointer;
        `;

        // 设置颜色
        const colors = {
            success: { bg: '#10B981', color: 'white' },
            error: { bg: '#EF4444', color: 'white' },
            info: { bg: '#3B82F6', color: 'white' },
            warning: { bg: '#F59E0B', color: 'white' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.color = color.color;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 10);

        // 点击关闭
        notification.addEventListener('click', () => {
            this.hide(notification);
        });

        // 自动关闭
        setTimeout(() => {
            this.hide(notification);
        }, duration);

        return notification;
    }

    hide(notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
}

// 全局通知实例
const notify = new NotificationSystem();

// 日期工具函数
const DateUtils = {
    formatDate(date) {
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        return date.toLocaleDateString('zh-CN', options);
    },

    formatMonth(date) {
        const options = { year: 'numeric', month: 'long' };
        return date.toLocaleDateString('zh-CN', options);
    },

    getDateKey(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },

    getWeekInfo(date) {
        const year = date.getFullYear();
        const start = new Date(year, 0, 1);
        const days = Math.floor((date - start) / (24 * 60 * 60 * 1000));
        const week = Math.ceil((days + start.getDay() + 1) / 7);
        return { year, week };
    },

    isToday(date) {
        const today = new Date();
        return this.getDateKey(date) === this.getDateKey(today);
    },

    isSameDay(date1, date2) {
        return this.getDateKey(date1) === this.getDateKey(date2);
    }
};

// 本地存储工具
const StorageUtils = {
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error(`读取存储数据失败 (${key}):`, error);
            return defaultValue;
        }
    },

    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error(`保存存储数据失败 (${key}):`, error);
            notify.show('保存数据失败，请检查浏览器存储设置', 'error');
            return false;
        }
    },

    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error(`删除存储数据失败 (${key}):`, error);
            return false;
        }
    },

    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('清空存储数据失败:', error);
            return false;
        }
    }
};

// DOM工具函数
const DOMUtils = {
    createElement(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    },

    getElement(selector) {
        return document.querySelector(selector);
    },

    getElements(selector) {
        return document.querySelectorAll(selector);
    },

    addClass(element, className) {
        if (element) element.classList.add(className);
    },

    removeClass(element, className) {
        if (element) element.classList.remove(className);
    },

    toggleClass(element, className) {
        if (element) element.classList.toggle(className);
    },

    hasClass(element, className) {
        return element ? element.classList.contains(className) : false;
    },

    show(element) {
        if (element) element.classList.remove('hidden');
    },

    hide(element) {
        if (element) element.classList.add('hidden');
    },

    toggle(element) {
        if (element) element.classList.toggle('hidden');
    }
};

// 数据验证工具
const ValidationUtils = {
    isNotEmpty(value) {
        return value && value.trim().length > 0;
    },

    isNumber(value) {
        return !isNaN(value) && !isNaN(parseFloat(value));
    },

    isPositiveNumber(value) {
        return this.isNumber(value) && parseFloat(value) > 0;
    },

    isEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    isValidDate(date) {
        return date instanceof Date && !isNaN(date);
    }
};

// 数据导出/导入工具
const DataUtils = {
    exportToJSON(data, filename = 'data') {
        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                data: data
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `${filename}_${DateUtils.getDateKey(new Date())}.json`;
            link.click();
            
            notify.show('数据导出成功', 'success');
            return true;
        } catch (error) {
            console.error('导出数据失败:', error);
            notify.show('导出数据失败', 'error');
            return false;
        }
    },

    importFromJSON(file, callback) {
        if (!file) {
            notify.show('请选择文件', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                
                if (!importedData.data) {
                    throw new Error('无效的数据格式');
                }
                
                if (callback && typeof callback === 'function') {
                    callback(importedData.data);
                }
                
                notify.show('数据导入成功', 'success');
            } catch (error) {
                console.error('导入数据失败:', error);
                notify.show('导入数据失败，请检查文件格式', 'error');
            }
        };
        
        reader.readAsText(file);
    }
};

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出到全局
window.notify = notify;
window.DateUtils = DateUtils;
window.StorageUtils = StorageUtils;
window.DOMUtils = DOMUtils;
window.ValidationUtils = ValidationUtils;
window.DataUtils = DataUtils;
window.debounce = debounce;
window.throttle = throttle;
