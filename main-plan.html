<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日计划 - 生活NOTE</title>
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="common-styles.css">
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Chart.js用于绘制图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <!-- 引入通用工具 -->
    <script src="common-utils.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#818CF8',
                        success: '#10B981',
                        study: '#3B82F6',    // 学习类别颜色
                        work: '#10B981',     // 工作类别颜色
                        life: '#F59E0B',     // 生活类别颜色
                        improvement: '#A78BFA', // 下次改进颜色
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .task-complete {
                @apply line-through text-gray-400;
            }
            .card-shadow {
                @apply shadow-md hover:shadow-lg transition-shadow duration-300;
            }
            .btn-effect {
                @apply transform hover:-translate-y-0.5 transition-transform duration-200;
            }
            .reflection-card {
                @apply bg-white border border-gray-100 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow;
            }
            .calendar-day {
                @apply w-8 h-8 flex items-center justify-center rounded-full cursor-pointer hover:bg-light;
            }
            .calendar-day-selected {
                @apply bg-primary text-white;
            }
            .calendar-day-today {
                @apply ring-2 ring-primary ring-offset-2;
            }
            .notification {
                @apply fixed bottom-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-y-10 opacity-0;
            }
            .notification.show {
                @apply translate-y-0 opacity-100;
            }
            .notification-success {
                @apply bg-success text-white;
            }
            .notification-error {
                @apply bg-red-500 text-white;
            }

            /* 导航栏样式 */
            .nav-link {
                color: #6B7280;
                font-weight: 500;
                transition: all 0.2s ease;
                padding: 0.5rem 0;
                position: relative;
                display: flex;
                align-items: center;
                text-decoration: none;
            }

            .nav-link:hover {
                color: #F59E0B;
            }

            .nav-link.active {
                color: #F59E0B;
                font-weight: 600;
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #F59E0B;
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .nav-link:hover::after,
            .nav-link.active::after {
                transform: scaleX(1);
            }

            .mobile-nav-link {
                color: #6B7280;
                border-radius: 0.375rem;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                text-decoration: none;
            }

            .mobile-nav-link:hover {
                color: #3B82F6;
                background-color: #F3F4F6;
            }

            .mobile-nav-link.active {
                color: #3B82F6;
                background-color: rgba(59, 130, 246, 0.1);
                font-weight: 600;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-dark">
    <!-- 统一导航栏 -->
    <nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between h-16">
                <!-- 左侧Logo/标题 -->
                <div class="flex items-center">
                    <a href="main-plan.html" class="flex items-center">
                        <i class="fa fa-apple text-secondary text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-dark">生活NOTE</span>
                    </a>
                </div>

                <!-- 右侧导航链接 - 桌面端 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="main-plan.html" class="nav-link active" data-page="main-plan">
                        <i class="fa fa-home mr-2"></i>主计划
                    </a>
                    <a href="fruit-plan.html" class="nav-link" data-page="fruit-plan">
                        <i class="fa fa-apple mr-2"></i>水果计划
                    </a>
                    <a href="expense-tracker.html" class="nav-link" data-page="expense-tracker">
                        <i class="fa fa-money mr-2"></i>支出记录
                    </a>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-primary focus:outline-none">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 移动端导航菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="main-plan.html" class="mobile-nav-link active block px-3 py-2 rounded-md" data-page="main-plan">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="fruit-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="fruit-plan">
                    <i class="fa fa-apple mr-2"></i>水果计划
                </a>
                <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="expense-tracker">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
            </div>
        </div>
    </nav>
    <div class="container mx-auto px-4 py-6 max-w-6xl" style="padding-top: 5rem;">
        <!-- 头部标题和日期 -->
        <header class="mb-6 text-center">
            <h1 class="text-[clamp(1.5rem,4vw,2.2rem)] font-bold text-primary mb-2">每日计划</h1>
            <div class="flex items-center justify-center space-x-3">
                <button id="prevDay" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-chevron-left"></i>
                </button>
                <h2 id="currentDate" class="text-xl font-medium"></h2>
                <button id="openCalendar" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-calendar"></i>
                </button>
                <button id="nextDay" class="p-2 rounded-full hover:bg-light text-primary">
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>
        </header>

        <!-- 日历模态框 -->
        <div id="calendarModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl w-full max-w-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-primary">选择日期</h3>
                    <button id="closeCalendar" class="text-gray-500 hover:text-gray-700">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="flex justify-between items-center mb-4">
                    <button id="prevMonth" class="p-1 hover:bg-light rounded">
                        <i class="fa fa-chevron-left"></i>
                    </button>
                    <h4 id="currentMonth" class="font-medium"></h4>
                    <button id="nextMonth" class="p-1 hover:bg-light rounded">
                        <i class="fa fa-chevron-right"></i>
                    </button>
                </div>
                <div class="grid grid-cols-7 gap-1 text-center mb-2">
                    <div class="text-sm text-gray-500">日</div>
                    <div class="text-sm text-gray-500">一</div>
                    <div class="text-sm text-gray-500">二</div>
                    <div class="text-sm text-gray-500">三</div>
                    <div class="text-sm text-gray-500">四</div>
                    <div class="text-sm text-gray-500">五</div>
                    <div class="text-sm text-gray-500">六</div>
                </div>
                <div id="calendarDays" class="grid grid-cols-7 gap-1 text-center">
                    <!-- 日历天数将动态生成 -->
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：计划列表和下次改进 -->
            <div class="lg:col-span-2">
                <!-- 今日计划 -->
                <div class="bg-white rounded-xl p-5 card-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold flex items-center text-primary">
                            <i class="fa fa-list-alt mr-2"></i> 今日计划
                        </h3>
                        <span id="taskStats" class="text-sm text-gray-500">
                            已完成 <span id="completedCount">0</span>/<span id="totalCount">0</span>
                        </span>
                    </div>

                    <!-- 添加新计划 -->
                    <div class="mb-5">
                        <div class="flex">
                            <input 
                                type="text" 
                                id="newTaskInput" 
                                placeholder="添加新的计划..." 
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                            >
                            <select id="taskCategory" class="border-y border-gray-300 px-3 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
                                <option value="study">学习</option>
                                <option value="work">工作</option>
                                <option value="life">生活</option>
                            </select>
                            <button id="addTaskBtn" class="bg-primary text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                        <div class="flex items-center mt-2 text-xs text-gray-500 space-x-4">
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-study mr-1"></span>
                                <span>学习</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-work mr-1"></span>
                                <span>工作</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-life mr-1"></span>
                                <span>生活</span>
                            </div>
                        </div>
                    </div>

                    <!-- 计划列表 -->
                    <div id="taskList" class="max-h-[300px] overflow-y-auto space-y-2">
                        <!-- 计划项将通过JavaScript动态生成 -->
                        <div id="emptyState" class="text-center py-10 text-gray-400">
                            <i class="fa fa-check-square-o text-4xl mb-3 opacity-30"></i>
                            <p>今天还没有计划，添加一个吧！</p>
                        </div>
                    </div>
                </div>

                <!-- 下次改进 -->
                <div class="bg-white rounded-xl p-5 card-shadow mt-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold flex items-center text-improvement">
                            <i class="fa fa-lightbulb-o mr-2"></i> 下次改进
                        </h3>
                        <span id="improvementStats" class="text-sm text-gray-500">
                            共 <span id="improvementCount">0</span> 项
                        </span>
                    </div>

                    <!-- 添加新改进项 -->
                    <div class="mb-5">
                        <div class="flex">
                            <input 
                                type="text" 
                                id="newImprovementInput" 
                                placeholder="添加下次可以改进的地方..." 
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-improvement/50 focus:border-improvement"
                            >
                            <button id="addImprovementBtn" class="bg-improvement text-white px-4 py-2 rounded-r-lg font-medium btn-effect">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-full bg-improvement mr-1"></span>
                                <span>记录需要改进的地方，不影响完成率</span>
                            </div>
                        </div>
                    </div>

                    <!-- 改进项列表 -->
                    <div id="improvementList" class="max-h-[200px] overflow-y-auto space-y-2">
                        <!-- 改进项将通过JavaScript动态生成 -->
                        <div id="emptyImprovementState" class="text-center py-10 text-gray-400">
                            <i class="fa fa-lightbulb-o text-4xl mb-3 opacity-30 text-improvement/50"></i>
                            <p>还没有添加改进项，记录一下下次可以做得更好的地方吧！</p>
                        </div>
                    </div>
                </div>

                <!-- 完成进度和评分 -->
                <div class="bg-white rounded-xl p-5 card-shadow mt-6">
                    <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                        <i class="fa fa-pie-chart mr-2"></i> 完成进度
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex justify-center">
                            <div class="relative w-32 h-32">
                                <svg class="w-full h-full" viewBox="0 0 36 36">
                                    <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="3" />
                                    <circle 
                                        id="progressRing" 
                                        cx="18" cy="18" r="16" 
                                        fill="none" stroke="#10B981" 
                                        stroke-width="3" 
                                        stroke-dasharray="100" 
                                        stroke-dashoffset="100" 
                                        transform="rotate(-90 18 18)"
                                        class="transition-all duration-1000 ease-out"
                                    />
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span id="progressPercent" class="text-2xl font-bold text-success">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 评分部分 -->
                        <div>
                            <h4 class="text-base font-medium mb-3">今日完成度评分</h4>
                            <div class="flex items-center space-x-2">
                                <div id="ratingStars" class="flex text-gray-300">
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="1"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="2"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="3"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="4"></i>
                                    <i class="fa fa-star text-xl cursor-pointer hover:text-yellow-400" data-rating="5"></i>
                                </div>
                                <span id="ratingText" class="text-gray-500 ml-2">未评分</span>
                            </div>
                            <div class="mt-3 space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">总计划</span>
                                    <span id="totalTasks">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">已完成</span>
                                    <span id="finishedTasks" class="text-success">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">未完成</span>
                                    <span id="pendingTasks">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：今日感想和数据导出 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl p-5 card-shadow h-full flex flex-col">
                    <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                        <i class="fa fa-pencil mr-2"></i> 今日感想
                    </h3>
                    
                    <!-- 添加新感想 -->
                    <div class="mb-6">
                        <textarea 
                            id="newReflectionInput" 
                            placeholder="添加新的感想..." 
                            class="w-full h-24 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary resize-none"
                        ></textarea>
                        <button id="addReflectionBtn" class="mt-3 bg-secondary text-white px-4 py-2 rounded-lg text-sm font-medium btn-effect">
                            <i class="fa fa-plus mr-1"></i> 添加
                        </button>
                    </div>
                    
                    <!-- 感想列表 -->
                    <div id="reflectionsContainer" class="flex-1 overflow-y-auto space-y-3 mb-6">
                        <!-- 感想卡片将通过JavaScript动态生成 -->
                        <div id="noReflections" class="text-center py-8 text-gray-400">
                            <i class="fa fa-comment-o text-3xl mb-2 opacity-30"></i>
                            <p>还没有添加感想</p>
                        </div>
                    </div>

                    <!-- 数据导出和导入 -->
                    <div class="border-t border-gray-100 pt-4">
                        <div class="flex flex-col space-y-2">
                            <button id="exportDataBtn" class="w-full bg-light text-primary px-4 py-2 rounded-lg text-sm font-medium btn-effect flex items-center justify-center">
                                <i class="fa fa-download mr-1"></i> 导出数据
                            </button>
                            <div class="relative">
                                <label for="importDataBtn" class="w-full bg-light text-primary px-4 py-2 rounded-lg text-sm font-medium btn-effect flex items-center justify-center cursor-pointer">
                                    <i class="fa fa-upload mr-1"></i> 导入数据
                                </label>
                                <input type="file" id="importDataBtn" accept=".json" class="absolute inset-0 opacity-0 cursor-pointer">
                            </div>
                        </div>
                        <p id="exportStatus" class="mt-2 text-sm text-center hidden"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="mt-6 text-center">
            <button id="showHistoryBtn" class="bg-light text-primary px-4 py-2 rounded-lg font-medium btn-effect">
                <i class="fa fa-history mr-1"></i> 查看历史记录
            </button>
        </div>

        <!-- 历史记录模态框 -->
        <div id="historyModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="text-lg font-bold">历史记录</h3>
                    <button id="closeHistoryBtn" class="text-white hover:text-gray-200">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="p-4 overflow-y-auto flex-1" id="historyContent">
                    <!-- 历史记录内容将通过JavaScript动态生成 -->
                </div>
                <div class="p-4 border-t border-gray-100">
                    <button id="clearHistoryBtn" class="text-red-500 hover:text-red-600 hover:bg-red-50 px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fa fa-trash mr-1"></i> 清除所有历史
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务完成率曲线 -->
        <div class="mt-8 bg-white rounded-xl p-5 card-shadow">
            <h3 class="text-lg font-bold flex items-center text-primary mb-4">
                <i class="fa fa-line-chart mr-2"></i> 任务完成率趋势
            </h3>
            <div class="h-64">
                <canvas id="completionRateChart"></canvas>
            </div>
            <p id="noHistoryData" class="text-center py-10 text-gray-400 hidden">
                <i class="fa fa-bar-chart text-3xl mb-2 opacity-30"></i>
                <p>暂无历史数据可显示</p>
            </p>
        </div>
    </div>

    <!-- 通知组件 -->
    <div id="notification" class="notification"></div>

    <script>
        // 确保DOM加载完成后再执行
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 日期处理工具
                const formatDate = (date) => {
                    const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                    return date.toLocaleDateString('zh-CN', options);
                };

                const formatMonth = (date) => {
                    const options = { year: 'numeric', month: 'long' };
                    return date.toLocaleDateString('zh-CN', options);
                };

                const getDateKey = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                // 全局状态
                let currentDate = new Date();
                let calendarDate = new Date(); // 用于日历显示的日期
                let appData = {};
                let completionChart = null; // 图表实例
                
                // 安全初始化本地存储数据
                try {
                    const storedData = localStorage.getItem('dailyPlanner');
                    if (storedData) {
                        appData = JSON.parse(storedData);
                    } else {
                        // 初始化新数据结构
                        appData = {
                            tasks: {},
                            improvements: {}, // 新增：下次改进项
                            reflections: {},
                            ratings: {},
                            history: {}
                        };
                    }
                } catch (e) {
                    console.error('本地存储数据解析错误，重新初始化:', e);
                    appData = {
                        tasks: {},
                        improvements: {}, // 新增：下次改进项
                        reflections: {},
                        ratings: {},
                        history: {}
                    };
                    localStorage.setItem('dailyPlanner', JSON.stringify(appData));
                }

                // 确保数据结构完整
                if (!appData.tasks) appData.tasks = {};
                if (!appData.improvements) appData.improvements = {}; // 新增：下次改进项
                if (!appData.reflections) appData.reflections = {};
                if (!appData.ratings) appData.ratings = {};
                if (!appData.history) appData.history = {};

                // DOM元素 - 增加存在性检查
                const elements = {};
                const elementIds = [
                    'currentDate', 'newTaskInput', 'addTaskBtn', 'taskList', 'emptyState',
                    'completedCount', 'totalCount', 'progressRing', 'progressPercent',
                    'totalTasks', 'finishedTasks', 'pendingTasks', 'ratingStars', 'ratingText',
                    'newReflectionInput', 'addReflectionBtn', 'reflectionsContainer', 'noReflections',
                    'prevDay', 'nextDay', 'showHistoryBtn', 'historyModal', 'closeHistoryBtn',
                    'historyContent', 'clearHistoryBtn', 'taskCategory', 'openCalendar',
                    'calendarModal', 'closeCalendar', 'currentMonth', 'prevMonth', 'nextMonth',
                    'calendarDays', 'exportDataBtn', 'exportStatus', 'importDataBtn', 'notification',
                    'completionRateChart', 'noHistoryData',
                    // 新增：下次改进相关元素
                    'newImprovementInput', 'addImprovementBtn', 'improvementList', 
                    'emptyImprovementState', 'improvementCount'
                ];
                
                elementIds.forEach(id => {
                    elements[id] = document.getElementById(id);
                    if (!elements[id]) {
                        console.warn(`元素 ${id} 未找到`);
                    }
                });

                // 显示通知
                const showNotification = (message, type = 'info') => {
                    if (!elements.notification) return;
                    
                    // 设置通知内容和样式
                    elements.notification.textContent = message;
                    elements.notification.className = `notification notification-${type} show`;
                    
                    // 3秒后隐藏
                    setTimeout(() => {
                        elements.notification.classList.remove('show');
                    }, 3000);
                };

                // 更新显示的日期
                const updateDisplayDate = () => {
                    if (elements.currentDate) {
                        elements.currentDate.textContent = formatDate(currentDate);
                    }
                };

                // 创建任务元素
                const createTaskElement = (task, index, totalTasks) => {
                    // 根据类别获取样式和图标
                    const categoryStyles = {
                        study: {
                            bgClass: 'bg-study',
                            icon: 'fa-book'
                        },
                        work: {
                            bgClass: 'bg-work',
                            icon: 'fa-briefcase'
                        },
                        life: {
                            bgClass: 'bg-life',
                            icon: 'fa-home'
                        }
                    };
                    
                    const style = categoryStyles[task.category] || categoryStyles.study;
                    
                    const taskEl = document.createElement('div');
                    taskEl.className = 'flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50';
                    taskEl.dataset.taskId = task.id;

                    taskEl.innerHTML = `
                        <input type="checkbox" class="task-checkbox w-5 h-5 rounded text-success focus:ring-success" ${task.completed ? 'checked' : ''}>
                        <span class="ml-2 ${task.completed ? 'task-complete' : ''}">
                            <span class="inline-block w-2 h-2 rounded-full ${style.bgClass} mr-2"></span>
                            ${task.text}
                        </span>
                        <div class="flex space-x-1 ml-auto">
                            <button class="move-task-up text-gray-400 hover:text-primary p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-task-down text-gray-400 hover:text-primary p-1 ${index === totalTasks - 1 ? 'opacity-30 cursor-not-allowed' : ''}">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-task text-gray-400 hover:text-red-500 p-1">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const checkbox = taskEl.querySelector('.task-checkbox');
                    if (checkbox) {
                        checkbox.addEventListener('change', () => {
                            toggleTaskCompletion(task.id);
                        });
                    }

                    const deleteBtn = taskEl.querySelector('.delete-task');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            deleteTask(task.id);
                        });
                    }

                    const moveUpBtn = taskEl.querySelector('.move-task-up');
                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveTask(task.id, -1); // 上移
                        });
                    }

                    const moveDownBtn = taskEl.querySelector('.move-task-down');
                    if (moveDownBtn && index < totalTasks - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveTask(task.id, 1); // 下移
                        });
                    }

                    return taskEl;
                };

                // 移动任务
                const moveTask = (taskId, direction) => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];
                    const taskIndex = tasks.findIndex(t => t.id === taskId);
                    
                    if (taskIndex === -1) return;
                    
                    // 计算新位置
                    const newIndex = taskIndex + direction;
                    
                    // 检查边界
                    if (newIndex < 0 || newIndex >= tasks.length) return;
                    
                    // 交换位置
                    [tasks[taskIndex], tasks[newIndex]] = [tasks[newIndex], tasks[taskIndex]];
                    
                    saveData();
                    loadTasks();
                    updateCompletionRateChart(); // 更新图表
                };

                // 加载任务列表
                const loadTasks = () => {
                    if (!elements.taskList || !elements.emptyState) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    // 清空列表
                    elements.taskList.innerHTML = '';

                    if (tasks.length === 0) {
                        // 显示空状态
                        elements.taskList.appendChild(elements.emptyState);
                    } else {
                        // 添加任务
                        tasks.forEach((task, index) => {
                            // 确保任务有category属性，兼容旧数据
                            if (!task.category) task.category = 'study';
                            const taskEl = createTaskElement(task, index, tasks.length);
                            elements.taskList.appendChild(taskEl);
                        });
                    }

                    // 更新统计
                    updateTaskStats();
                };

                // 添加新任务
                const addTask = () => {
                    if (!elements.newTaskInput || !elements.taskCategory) return;
                    
                    const taskText = elements.newTaskInput.value.trim();
                    if (!taskText) {
                        showNotification('请输入任务内容', 'error');
                        return;
                    }

                    const category = elements.taskCategory.value;
                    const dateKey = getDateKey(currentDate);
                    if (!appData.tasks[dateKey]) {
                        appData.tasks[dateKey] = [];
                    }

                    const newTask = {
                        id: Date.now().toString(),
                        text: taskText,
                        completed: false,
                        category: category
                    };

                    appData.tasks[dateKey].push(newTask);
                    saveData();
                    loadTasks();
                    updateCompletionRateChart(); // 更新图表

                    // 清空输入框
                    elements.newTaskInput.value = '';
                    elements.newTaskInput.focus();
                    showNotification('任务添加成功', 'success');
                };

                // 切换任务完成状态
                const toggleTaskCompletion = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];
                    
                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.completed = !task.completed;
                        saveData();
                        loadTasks();
                        updateCompletionRateChart(); // 更新图表
                    }
                };

                // 删除任务
                const deleteTask = (taskId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.tasks[dateKey]) {
                        appData.tasks[dateKey] = appData.tasks[dateKey].filter(t => t.id !== taskId);
                        saveData();
                        loadTasks();
                        updateCompletionRateChart(); // 更新图表
                        showNotification('任务已删除', 'success');
                    }
                };

                // 更新任务统计
                const updateTaskStats = () => {
                    const dateKey = getDateKey(currentDate);
                    const tasks = appData.tasks[dateKey] || [];

                    const total = tasks.length;
                    const completed = tasks.filter(t => t.completed).length;
                    const pending = total - completed;
                    const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

                    // 更新进度环
                    if (elements.progressRing) {
                        const circumference = 2 * Math.PI * 16; // 圆周长
                        const offset = circumference - (progress / 100) * circumference;
                        elements.progressRing.style.strokeDasharray = `${circumference} ${circumference}`;
                        elements.progressRing.style.strokeDashoffset = offset;
                    }

                    // 更新统计数字
                    if (elements.progressPercent) elements.progressPercent.textContent = `${progress}%`;
                    if (elements.totalCount) elements.totalCount.textContent = total;
                    if (elements.completedCount) elements.completedCount.textContent = completed;
                    if (elements.totalTasks) elements.totalTasks.textContent = total;
                    if (elements.finishedTasks) elements.finishedTasks.textContent = completed;
                    if (elements.pendingTasks) elements.pendingTasks.textContent = pending;
                    
                    // 更新评分显示
                    updateRatingDisplay();
                };

                // 新增：创建改进项元素
                const createImprovementElement = (improvement, index, totalImprovements) => {
                    const improvementEl = document.createElement('div');
                    improvementEl.className = 'flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50';
                    improvementEl.dataset.improvementId = improvement.id;

                    improvementEl.innerHTML = `
                        <span class="ml-2">
                            <span class="inline-block w-2 h-2 rounded-full bg-improvement mr-2"></span>
                            ${improvement.text}
                        </span>
                        <div class="flex space-x-1 ml-auto">
                            <button class="move-improvement-up text-gray-400 hover:text-improvement p-1 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}">
                                <i class="fa fa-chevron-up"></i>
                            </button>
                            <button class="move-improvement-down text-gray-400 hover:text-improvement p-1 ${index === totalImprovements - 1 ? 'opacity-30 cursor-not-allowed' : ''}">
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <button class="delete-improvement text-gray-400 hover:text-red-500 p-1">
                                <i class="fa fa-trash-o"></i>
                            </button>
                        </div>
                    `;

                    // 添加事件监听
                    const deleteBtn = improvementEl.querySelector('.delete-improvement');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            deleteImprovement(improvement.id);
                        });
                    }

                    const moveUpBtn = improvementEl.querySelector('.move-improvement-up');
                    if (moveUpBtn && index > 0) {
                        moveUpBtn.addEventListener('click', () => {
                            moveImprovement(improvement.id, -1); // 上移
                        });
                    }

                    const moveDownBtn = improvementEl.querySelector('.move-improvement-down');
                    if (moveDownBtn && index < totalImprovements - 1) {
                        moveDownBtn.addEventListener('click', () => {
                            moveImprovement(improvement.id, 1); // 下移
                        });
                    }

                    return improvementEl;
                };

                // 新增：移动改进项
                const moveImprovement = (improvementId, direction) => {
                    const dateKey = getDateKey(currentDate);
                    const improvements = appData.improvements[dateKey] || [];
                    const improvementIndex = improvements.findIndex(i => i.id === improvementId);
                    
                    if (improvementIndex === -1) return;
                    
                    // 计算新位置
                    const newIndex = improvementIndex + direction;
                    
                    // 检查边界
                    if (newIndex < 0 || newIndex >= improvements.length) return;
                    
                    // 交换位置
                    [improvements[improvementIndex], improvements[newIndex]] = [improvements[newIndex], improvements[improvementIndex]];
                    
                    saveData();
                    loadImprovements();
                };

                // 新增：加载改进项列表
                const loadImprovements = () => {
                    if (!elements.improvementList || !elements.emptyImprovementState) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const improvements = appData.improvements[dateKey] || [];

                    // 清空列表
                    elements.improvementList.innerHTML = '';

                    // 更新改进项计数
                    if (elements.improvementCount) {
                        elements.improvementCount.textContent = improvements.length;
                    }

                    if (improvements.length === 0) {
                        // 显示空状态
                        elements.improvementList.appendChild(elements.emptyImprovementState);
                    } else {
                        // 添加改进项
                        improvements.forEach((improvement, index) => {
                            const improvementEl = createImprovementElement(improvement, index, improvements.length);
                            elements.improvementList.appendChild(improvementEl);
                        });
                    }
                };

                // 新增：添加新改进项
                const addImprovement = () => {
                    if (!elements.newImprovementInput) return;
                    
                    const improvementText = elements.newImprovementInput.value.trim();
                    if (!improvementText) {
                        showNotification('请输入改进内容', 'error');
                        return;
                    }

                    const dateKey = getDateKey(currentDate);
                    if (!appData.improvements[dateKey]) {
                        appData.improvements[dateKey] = [];
                    }

                    const newImprovement = {
                        id: Date.now().toString(),
                        text: improvementText
                    };

                    appData.improvements[dateKey].push(newImprovement);
                    saveData();
                    loadImprovements();

                    // 清空输入框
                    elements.newImprovementInput.value = '';
                    elements.newImprovementInput.focus();
                    showNotification('改进项添加成功', 'success');
                };

                // 新增：删除改进项
                const deleteImprovement = (improvementId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.improvements[dateKey]) {
                        appData.improvements[dateKey] = appData.improvements[dateKey].filter(i => i.id !== improvementId);
                        saveData();
                        loadImprovements();
                        showNotification('改进项已删除', 'success');
                    }
                };

                // 设置评分
                const setRating = (rating) => {
                    const dateKey = getDateKey(currentDate);
                    appData.ratings[dateKey] = rating;
                    saveData();
                    updateRatingDisplay();
                    showNotification('评分已保存', 'success');
                };

                // 更新评分显示
                const updateRatingDisplay = () => {
                    if (!elements.ratingStars || !elements.ratingText) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const rating = appData.ratings[dateKey] || 0;
                    
                    // 更新星星显示
                    const stars = elements.ratingStars.querySelectorAll('i');
                    stars.forEach((star, index) => {
                        if (index + 1 <= rating) {
                            star.classList.remove('text-gray-300');
                            star.classList.add('text-yellow-400');
                        } else {
                            star.classList.remove('text-yellow-400');
                            star.classList.add('text-gray-300');
                        }
                    });
                    
                    // 更新评分文本
                    const ratingTexts = ['未评分', '很差', '一般', '良好', '优秀', '完美'];
                    elements.ratingText.textContent = ratingTexts[rating];
                };

                // 创建感想卡片
                const createReflectionCard = (reflection) => {
                    const card = document.createElement('div');
                    card.className = 'reflection-card';
                    card.dataset.reflectionId = reflection.id;
                    
                    // 格式化时间
                    const date = new Date(reflection.timestamp);
                    const timeString = date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    
                    card.innerHTML = `
                        <div class="flex justify-between items-start mb-2">
                            <p class="text-sm text-gray-500">${timeString}</p>
                            <div class="flex space-x-1">
                                <button class="edit-reflection text-gray-400 hover:text-primary p-1">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="delete-reflection text-gray-400 hover:text-red-500 p-1">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-gray-800 mb-2">${reflection.text}</p>
                        
                        <!-- 编辑区域 (默认隐藏) -->
                        <div class="edit-reflection-container hidden mt-2">
                            <textarea class="w-full p-2 border border-gray-200 rounded text-sm" rows="2">${reflection.text}</textarea>
                            <div class="flex justify-end space-x-2 mt-2">
                                <button class="cancel-edit text-gray-500 px-2 py-1 rounded text-xs">取消</button>
                                <button class="save-edit bg-primary text-white px-2 py-1 rounded text-xs">保存</button>
                            </div>
                            <p class="edit-reflection-message text-xs mt-1 hidden"></p>
                        </div>
                    `;
                    
                    // 添加事件监听
                    const editBtn = card.querySelector('.edit-reflection');
                    const deleteBtn = card.querySelector('.delete-reflection');
                    const cancelBtn = card.querySelector('.cancel-edit');
                    const saveBtn = card.querySelector('.save-edit');
                    const messageEl = card.querySelector('.edit-reflection-message');
                    const cardContent = card.querySelector('p.text-gray-800');
                    const editContainer = card.querySelector('.edit-reflection-container');
                    const editTextarea = card.querySelector('.edit-reflection-container textarea');
                    
                    if (editBtn && cardContent && editContainer) {
                        editBtn.addEventListener('click', () => {
                            cardContent.classList.add('hidden');
                            editContainer.classList.remove('hidden');
                            if (editTextarea) editTextarea.focus();
                            if (messageEl) messageEl.classList.add('hidden');
                        });
                    }
                    
                    if (cancelBtn && cardContent && editContainer) {
                        cancelBtn.addEventListener('click', () => {
                            cardContent.classList.remove('hidden');
                            editContainer.classList.add('hidden');
                            if (messageEl) messageEl.classList.add('hidden');
                        });
                    }
                    
                    if (saveBtn && editTextarea && messageEl) {
                        saveBtn.addEventListener('click', () => {
                            const newText = editTextarea.value.trim();
                            if (!newText) {
                                messageEl.textContent = '内容不能为空';
                                messageEl.classList.remove('hidden', 'text-green-500');
                                messageEl.classList.add('text-red-500');
                                return;
                            }
                            
                            updateReflection(reflection.id, newText);
                            messageEl.textContent = '修改成功';
                            messageEl.classList.remove('hidden', 'text-red-500');
                            messageEl.classList.add('text-green-500');
                            
                            // 2秒后隐藏编辑区域
                            setTimeout(() => {
                                cardContent.classList.remove('hidden');
                                editContainer.classList.add('hidden');
                            }, 1000);
                        });
                    }
                    
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => {
                            if (confirm('确定要删除这条感想吗？')) {
                                deleteReflection(reflection.id);
                            }
                        });
                    }
                    
                    return card;
                };

                // 加载感想列表
                const loadReflections = () => {
                    if (!elements.reflectionsContainer || !elements.noReflections) return;
                    
                    const dateKey = getDateKey(currentDate);
                    const reflections = appData.reflections[dateKey] || [];
                    
                    // 清空容器
                    elements.reflectionsContainer.innerHTML = '';
                    
                    if (reflections.length === 0) {
                        // 显示无感想状态
                        elements.reflectionsContainer.appendChild(elements.noReflections);
                    } else {
                        // 按时间排序，最新的在前面
                        const sortedReflections = [...reflections].sort((a, b) => b.timestamp - a.timestamp);
                        
                        // 添加感想卡片
                        sortedReflections.forEach(reflection => {
                            const card = createReflectionCard(reflection);
                            elements.reflectionsContainer.appendChild(card);
                        });
                    }
                };

                // 添加新感想
                const addReflection = () => {
                    if (!elements.newReflectionInput) return;
                    
                    const text = elements.newReflectionInput.value.trim();
                    if (!text) {
                        showNotification('请输入感想内容', 'error');
                        return;
                    }
                    
                    const dateKey = getDateKey(currentDate);
                    if (!appData.reflections[dateKey]) {
                        appData.reflections[dateKey] = [];
                    }
                    
                    const newReflection = {
                        id: Date.now().toString(),
                        text: text,
                        timestamp: Date.now()
                    };
                    
                    appData.reflections[dateKey].push(newReflection);
                    saveData();
                    loadReflections();
                    
                    // 清空输入框
                    elements.newReflectionInput.value = '';
                    elements.newReflectionInput.focus();
                    showNotification('感想添加成功', 'success');
                };

                // 更新感想
                const updateReflection = (reflectionId, newText) => {
                    const dateKey = getDateKey(currentDate);
                    const reflections = appData.reflections[dateKey] || [];
                    
                    const reflection = reflections.find(r => r.id === reflectionId);
                    if (reflection) {
                        reflection.text = newText;
                        reflection.timestamp = Date.now(); // 更新时间戳
                        saveData();
                        loadReflections();
                    }
                };

                // 删除感想
                const deleteReflection = (reflectionId) => {
                    const dateKey = getDateKey(currentDate);
                    if (appData.reflections[dateKey]) {
                        appData.reflections[dateKey] = appData.reflections[dateKey].filter(r => r.id !== reflectionId);
                        saveData();
                        loadReflections();
                        showNotification('感想已删除', 'success');
                    }
                };

                // 切换日期
                const changeDate = (days) => {
                    // 保存当前日期的数据到历史
                    archiveCurrentDate();
                    
                    currentDate.setDate(currentDate.getDate() + days);
                    updateDisplayDate();
                    loadTasks();
                    loadImprovements(); // 新增：加载改进项
                    loadReflections();
                    updateRatingDisplay();
                    updateCompletionRateChart(); // 更新图表
                };

                // 切换到指定日期
                const goToDate = (year, month, day) => {
                    // 保存当前日期的数据到历史
                    archiveCurrentDate();
                    
                    currentDate = new Date(year, month, day);
                    if (elements.calendarModal) {
                        elements.calendarModal.classList.add('hidden');
                    }
                    updateDisplayDate();
                    loadTasks();
                    loadImprovements(); // 新增：加载改进项
                    loadReflections();
                    updateRatingDisplay();
                    updateCompletionRateChart(); // 更新图表
                };

                // 生成日历
                const generateCalendar = () => {
                    if (!elements.calendarDays || !elements.currentMonth) return;
                    
                    const year = calendarDate.getFullYear();
                    const month = calendarDate.getMonth();
                    
                    // 更新月份显示
                    elements.currentMonth.textContent = formatMonth(calendarDate);
                    
                    // 获取当月第一天是星期几
                    const firstDay = new Date(year, month, 1).getDay();
                    
                    // 获取当月的天数
                    const daysInMonth = new Date(year, month + 1, 0).getDate();
                    
                    // 清空日历
                    elements.calendarDays.innerHTML = '';
                    
                    // 添加空白格子
                    for (let i = 0; i < firstDay; i++) {
                        const emptyDay = document.createElement('div');
                        elements.calendarDays.appendChild(emptyDay);
                    }
                    
                    // 添加日期
                    const today = new Date();
                    const currentDateKey = getDateKey(currentDate);
                    
                    for (let day = 1; day <= daysInMonth; day++) {
                        const dayEl = document.createElement('div');
                        dayEl.className = 'calendar-day';
                        dayEl.textContent = day;
                        
                        // 检查是否是今天
                        const isToday = day === today.getDate() && 
                                       month === today.getMonth() && 
                                       year === today.getFullYear();
                        
                        // 检查是否是当前选中的日期
                        const dateKey = getDateKey(new Date(year, month, day));
                        const isSelected = dateKey === currentDateKey;
                        
                        // 添加相应的样式
                        if (isSelected) {
                            dayEl.classList.add('calendar-day-selected');
                        } else if (isToday) {
                            dayEl.classList.add('calendar-day-today');
                        }
                        
                        // 添加点击事件
                        dayEl.addEventListener('click', () => {
                            goToDate(year, month, day);
                        });
                        
                        elements.calendarDays.appendChild(dayEl);
                    }
                };

                // 切换月份
                const changeMonth = (months) => {
                    calendarDate.setMonth(calendarDate.getMonth() + months);
                    generateCalendar();
                };

                // 将当前日期数据存档到历史
                const archiveCurrentDate = () => {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    
                    const current = new Date(currentDate);
                    current.setHours(0, 0, 0, 0);
                    
                    // 只存档过去的日期
                    if (current < today) {
                        const dateKey = getDateKey(currentDate);
                        appData.history[dateKey] = {
                            tasks: [...(appData.tasks[dateKey] || [])],
                            improvements: [...(appData.improvements[dateKey] || [])], // 新增：存档改进项
                            reflections: [...(appData.reflections[dateKey] || [])],
                            rating: appData.ratings[dateKey] || 0
                        };
                        saveData();
                        updateCompletionRateChart(); // 更新图表
                    }
                };

                // 显示历史记录
                const showHistory = () => {
                    if (!elements.historyContent || !elements.historyModal) return;
                    
                    const historyDates = Object.keys(appData.history).sort((a, b) => new Date(b) - new Date(a));
                    
                    if (historyDates.length === 0) {
                        elements.historyContent.innerHTML = `
                            <div class="text-center py-10 text-gray-400">
                                <i class="fa fa-folder-open-o text-3xl mb-2"></i>
                                <p>暂无历史记录</p>
                            </div>
                        `;
                    } else {
                        elements.historyContent.innerHTML = '';
                        
                        historyDates.forEach(dateKey => {
                            const historyItem = createHistoryItem(dateKey);
                            elements.historyContent.appendChild(historyItem);
                        });
                    }
                    
                    elements.historyModal.classList.remove('hidden');
                };

                // 创建历史记录项
                const createHistoryItem = (dateKey) => {
                    const history = appData.history[dateKey];
                    const date = new Date(dateKey);
                    const completedTasks = history.tasks.filter(t => t.completed).length;
                    const totalTasks = history.tasks.length;
                    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
                    
                    // 评分文本
                    const ratingTexts = ['未评分', '很差', '一般', '良好', '优秀', '完美'];
                    const ratingText = ratingTexts[history.rating];
                    
                    const item = document.createElement('div');
                    item.className = 'border border-gray-100 rounded-lg p-3 mb-3 hover:bg-gray-50';
                    
                    item.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium">${formatDate(date)}</h4>
                            <div class="flex items-center">
                                <span class="text-yellow-400 mr-1">
                                    ${Array(history.rating).fill('<i class="fa fa-star text-xs"></i>').join('')}
                                </span>
                                <span class="text-xs text-gray-500">${ratingText}</span>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            完成 ${completedTasks}/${totalTasks} 项任务 (${completionRate}%)
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            ${history.improvements.length} 项改进建议
                        </div>
                        <div class="text-sm text-gray-600 mb-3">
                            ${history.reflections.length} 条感想
                        </div>
                        <button class="view-history-item text-primary text-sm">
                            <i class="fa fa-eye mr-1"></i> 查看详情
                        </button>
                    `;
                    
                    const viewBtn = item.querySelector('.view-history-item');
                    if (viewBtn && elements.historyModal) {
                        viewBtn.addEventListener('click', () => {
                            elements.historyModal.classList.add('hidden');
                            viewHistoryItem(dateKey);
                        });
                    }
                    
                    return item;
                };

                // 查看历史记录详情
                const viewHistoryItem = (dateKey) => {
                    // 切换到该日期
                    const [year, month, day] = dateKey.split('-').map(Number);
                    currentDate = new Date(year, month - 1, day);
                    
                    // 加载数据
                    loadTasks();
                    loadImprovements(); // 新增：加载改进项
                    loadReflections();
                    updateRatingDisplay();
                    updateDisplayDate();
                };

                // 清除历史记录
                const clearHistory = () => {
                    if (confirm('确定要清除所有历史记录吗？此操作无法撤销。')) {
                        appData.history = {};
                        saveData();
                        showHistory();
                        updateCompletionRateChart(); // 更新图表
                        showNotification('历史记录已清除', 'success');
                    }
                };

                // 导出数据
                const exportData = () => {
                    try {
                        // 创建要导出的数据
                        const exportData = {
                            timestamp: new Date().toISOString(),
                            data: appData
                        };
                        
                        // 转换为JSON字符串
                        const jsonString = JSON.stringify(exportData, null, 2);
                        
                        // 创建Blob对象
                        const blob = new Blob([jsonString], { type: 'application/json' });
                        
                        // 创建下载链接
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `daily-planner-data-${new Date().toISOString().split('T')[0]}.json`;
                        document.body.appendChild(a);
                        
                        // 触发下载
                        a.click();
                        
                        // 清理
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        
                        // 显示成功消息
                        showNotification('数据导出成功', 'success');
                    } catch (e) {
                        console.error('数据导出失败:', e);
                        showNotification('数据导出失败，请重试', 'error');
                    }
                };

                // 导入数据
                const importData = (file) => {
                    const reader = new FileReader();
                    
                    reader.onload = (e) => {
                        try {
                            const importedData = JSON.parse(e.target.result);
                            
                            // 验证导入的数据格式
                            if (!importedData.data || 
                                typeof importedData.data !== 'object' ||
                                !('tasks' in importedData.data) ||
                                !('improvements' in importedData.data) || // 新增：验证改进项数据
                                !('reflections' in importedData.data) ||
                                !('ratings' in importedData.data) ||
                                !('history' in importedData.data)) {
                                
                                showNotification('导入失败：无效的数据格式', 'error');
                                return;
                            }
                            
                            // 确认是否覆盖现有数据
                            if (confirm('确定要导入数据吗？这将覆盖当前所有数据。')) {
                                appData = importedData.data;
                                saveData();
                                
                                // 重新加载当前视图
                                loadTasks();
                                loadImprovements(); // 新增：加载改进项
                                loadReflections();
                                updateRatingDisplay();
                                updateCompletionRateChart(); // 更新图表
                                
                                showNotification('数据导入成功', 'success');
                            }
                        } catch (error) {
                            console.error('导入数据解析失败:', error);
                            showNotification('导入失败：数据解析错误', 'error');
                        }
                    };
                    
                    reader.onerror = () => {
                        console.error('文件读取错误');
                        showNotification('导入失败：文件读取错误', 'error');
                    };
                    
                    reader.readAsText(file);
                };

                // 保存数据到本地存储
                const saveData = () => {
                    try {
                        localStorage.setItem('dailyPlanner', JSON.stringify(appData));
                    } catch (e) {
                        console.error('保存数据失败:', e);
                        showNotification('保存数据失败，请检查浏览器存储设置', 'error');
                    }
                };

                // 准备完成率图表数据
                const prepareCompletionRateData = () => {
                    // 获取所有历史记录日期并排序
                    const historyDates = Object.keys(appData.history).sort((a, b) => new Date(a) - new Date(b));
                    
                    // 如果没有历史数据
                    if (historyDates.length === 0) {
                        return null;
                    }
                    
                    // 准备标签和数据
                    const labels = [];
                    const completionRates = [];
                    
                    historyDates.forEach(dateKey => {
                        const history = appData.history[dateKey];
                        const completedTasks = history.tasks.filter(t => t.completed).length;
                        const totalTasks = history.tasks.length;
                        const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
                        
                        // 格式化日期标签
                        const date = new Date(dateKey);
                        const label = `${date.getMonth() + 1}月${date.getDate()}日`;
                        
                        labels.push(label);
                        completionRates.push(completionRate);
                    });
                    
                    return { labels, completionRates };
                };

                // 更新任务完成率图表
                const updateCompletionRateChart = () => {
                    if (!elements.completionRateChart || !elements.noHistoryData) return;
                    
                    const chartData = prepareCompletionRateData();
                    
                    // 显示或隐藏"无数据"提示
                    if (!chartData || chartData.labels.length === 0) {
                        if (elements.noHistoryData) {
                            elements.noHistoryData.classList.remove('hidden');
                        }
                        // 如果已有图表实例，销毁它
                        if (completionChart) {
                            completionChart.destroy();
                            completionChart = null;
                        }
                        return;
                    } else {
                        if (elements.noHistoryData) {
                            elements.noHistoryData.classList.add('hidden');
                        }
                    }
                    
                    // 销毁现有图表（如果存在）
                    if (completionChart) {
                        completionChart.destroy();
                    }
                    
                    // 创建新图表
                    const ctx = elements.completionRateChart.getContext('2d');
                    completionChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: chartData.labels,
                            datasets: [{
                                label: '任务完成率 (%)',
                                data: chartData.completionRates,
                                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                                borderColor: 'rgba(79, 70, 229, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true,
                                pointBackgroundColor: 'rgba(79, 70, 229, 1)',
                                pointRadius: 4,
                                pointHoverRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `完成率: ${context.raw}%`;
                                        }
                                    }
                                },
                                legend: {
                                    display: false
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            }
                        }
                    });
                };

                // 注册事件监听
                const setupEventListeners = () => {
                    // 添加任务
                    if (elements.addTaskBtn) {
                        elements.addTaskBtn.addEventListener('click', addTask);
                    }
                    
                    if (elements.newTaskInput) {
                        elements.newTaskInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addTask();
                        });
                    }

                    // 新增：添加改进项
                    if (elements.addImprovementBtn) {
                        elements.addImprovementBtn.addEventListener('click', addImprovement);
                    }
                    
                    if (elements.newImprovementInput) {
                        elements.newImprovementInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') addImprovement();
                        });
                    }

                    // 评分星星点击事件
                    if (elements.ratingStars) {
                        const stars = elements.ratingStars.querySelectorAll('i');
                        stars.forEach((star, index) => {
                            star.addEventListener('click', () => {
                                setRating(index + 1);
                            });
                        });
                    }

                    // 感想相关
                    if (elements.addReflectionBtn) {
                        elements.addReflectionBtn.addEventListener('click', addReflection);
                    }
                    
                    if (elements.newReflectionInput) {
                        elements.newReflectionInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                addReflection();
                            }
                        });
                    }

                    // 日期导航
                    if (elements.prevDay) {
                        elements.prevDay.addEventListener('click', () => changeDate(-1));
                    }
                    
                    if (elements.nextDay) {
                        elements.nextDay.addEventListener('click', () => changeDate(1));
                    }

                    // 日历相关
                    if (elements.openCalendar) {
                        elements.openCalendar.addEventListener('click', () => {
                            if (elements.calendarModal) {
                                elements.calendarModal.classList.remove('hidden');
                                // 更新日历日期为当前显示的日期
                                calendarDate = new Date(currentDate);
                                generateCalendar();
                            }
                        });
                    }
                    
                    if (elements.closeCalendar) {
                        elements.closeCalendar.addEventListener('click', () => {
                            if (elements.calendarModal) {
                                elements.calendarModal.classList.add('hidden');
                            }
                        });
                    }
                    
                    if (elements.prevMonth) {
                        elements.prevMonth.addEventListener('click', () => changeMonth(-1));
                    }
                    
                    if (elements.nextMonth) {
                        elements.nextMonth.addEventListener('click', () => changeMonth(1));
                    }

                    // 数据导出和导入
                    if (elements.exportDataBtn) {
                        elements.exportDataBtn.addEventListener('click', exportData);
                    }
                    
                    if (elements.importDataBtn) {
                        elements.importDataBtn.addEventListener('change', (e) => {
                            if (e.target.files && e.target.files[0]) {
                                importData(e.target.files[0]);
                                // 重置文件输入，允许重复选择同一个文件
                                e.target.value = '';
                            }
                        });
                    }

                    // 历史记录
                    if (elements.showHistoryBtn) {
                        elements.showHistoryBtn.addEventListener('click', showHistory);
                    }
                    
                    if (elements.closeHistoryBtn && elements.historyModal) {
                        elements.closeHistoryBtn.addEventListener('click', () => {
                            elements.historyModal.classList.add('hidden');
                        });
                    }
                    
                    if (elements.clearHistoryBtn) {
                        elements.clearHistoryBtn.addEventListener('click', clearHistory);
                    }

                    // 点击模态框外部关闭
                    if (elements.historyModal) {
                        elements.historyModal.addEventListener('click', (e) => {
                            if (e.target === elements.historyModal) {
                                elements.historyModal.classList.add('hidden');
                            }
                        });
                    }
                    
                    if (elements.calendarModal) {
                        elements.calendarModal.addEventListener('click', (e) => {
                            if (e.target === elements.calendarModal) {
                                elements.calendarModal.classList.add('hidden');
                            }
                        });
                    }
                };

                // 初始化应用
                const initApp = () => {
                    console.log('初始化应用...');
                    updateDisplayDate();
                    loadTasks();
                    loadImprovements(); // 新增：加载改进项
                    loadReflections();
                    updateRatingDisplay();
                    setupEventListeners();
                    updateCompletionRateChart(); // 初始化图表
                    console.log('应用初始化完成');
                };

                // 启动应用
                initApp();
                
            } catch (e) {
                console.error('应用初始化失败:', e);
                alert('应用加载失败，请刷新页面重试。如果问题持续，请检查浏览器设置。');
            }
        });
    </script>

    <!-- 导航栏功能脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 移动端菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // 滚动时导航栏样式变化（加深阴影）
            window.addEventListener('scroll', function() {
                const nav = document.querySelector('nav');
                if (nav) {
                    if (window.scrollY > 10) {
                        nav.classList.add('shadow-lg');
                    } else {
                        nav.classList.remove('shadow-lg');
                    }
                }
            });
        });
    </script>
</body>
</html>
