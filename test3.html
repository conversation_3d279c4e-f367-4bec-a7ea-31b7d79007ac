<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>我的愿望博客 | WishBlog</title>
  <!-- 引入外部资源 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind主题 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#6366F1', // 主色调：靛蓝色
            secondary: '#EC4899', // 辅助色：粉色
            accent: '#10B981', // 强调色：绿色
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
          animation: {
            'float': 'float 3s ease-in-out infinite',
            'spin-slow': 'spin 10s linear infinite',
            'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.3s ease-out',
            'bounce-subtle': 'bounceSubtle 2s infinite',
          },
          keyframes: {
            float: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-10px)' },
            },
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            bounceSubtle: {
              '0%, 100%': { transform: 'translateY(-2px)' },
              '50%': { transform: 'translateY(0)' },
            }
          }
        }
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .backdrop-blur {
        backdrop-filter: blur(8px);
      }
      .form-focus {
        @apply focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all outline-none;
      }
    }
  </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-indigo-50 min-h-screen font-sans text-gray-800">
  <!-- 装饰元素 - 浮动星星和形状 -->
  <div class="fixed inset-0 overflow-hidden pointer-events-none z-0">
    <div class="absolute top-1/4 left-1/5 w-6 h-6 rounded-full bg-yellow-300 animate-pulse-slow opacity-70"></div>
    <div class="absolute top-1/3 right-1/4 w-4 h-4 rounded-full bg-purple-300 animate-pulse-slow opacity-60" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-1/4 left-1/3 w-5 h-5 rounded-full bg-pink-300 animate-pulse-slow opacity-80" style="animation-delay: 2s;"></div>
    <div class="absolute bottom-1/3 right-1/5 w-3 h-3 rounded-full bg-blue-300 animate-pulse-slow opacity-50" style="animation-delay: 1.5s;"></div>
    <div class="absolute top-1/2 left-1/4 w-8 h-8 rounded-full bg-indigo-200 animate-pulse-slow opacity-40" style="animation-delay: 0.5s;"></div>
  </div>

  <!-- 页面容器 -->
  <div class="container mx-auto px-4 py-8 relative z-10 max-w-6xl">
    <!-- 标题区域 -->
    <header class="text-center mb-10">
      <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-primary mb-4 text-shadow">
        <i class="fa fa-comments text-yellow-400 animate-spin-slow"></i>
        我的愿望博客
        <i class="fa fa-lightbulb-o text-yellow-400 animate-spin-slow" style="animation-direction: reverse;"></i>
      </h1>
      <p class="text-gray-600 max-w-2xl mx-auto text-lg">记录生活中的每一个愿望，添加细节、图片，追踪实现过程，让梦想有迹可循</p>
    </header>

    <!-- 主要内容区域 -->
    <main>
      <!-- 添加愿望按钮 -->
      <div class="text-center mb-8">
        <button id="showAddWishBtn" class="bg-primary hover:bg-primary/90 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-flex items-center">
          <i class="fa fa-plus-circle mr-2"></i> 添加新愿望
        </button>
      </div>

      <!-- 添加愿望表单 (默认隐藏) -->
      <div id="wishFormContainer" class="bg-white rounded-2xl p-6 md:p-8 shadow-xl animate-fade-in border border-gray-100 mb-12 relative overflow-hidden hidden">
        <!-- 装饰元素 -->
        <div class="absolute -top-10 -right-10 w-32 h-32 bg-primary/10 rounded-full"></div>
        <div class="absolute -bottom-12 -left-12 w-32 h-32 bg-secondary/10 rounded-full"></div>
        
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-semibold text-primary flex items-center relative z-10">
            <i class="fa fa-plus-circle mr-3 text-xl"></i> 记录新愿望
            <span class="ml-3 text-sm font-normal text-gray-500">把你的梦想写下来</span>
          </h2>
          <button id="hideAddWishBtn" class="text-gray-500 hover:text-gray-700 transition-colors p-2">
            <i class="fa fa-times text-xl"></i>
          </button>
        </div>
        
        <form id="wishForm" class="space-y-6 relative z-10">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2 space-y-1">
              <label for="wishTitle" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <i class="fa fa-heading text-primary/70 mr-2"></i>愿望标题
              </label>
              <input 
                type="text" 
                id="wishTitle" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg form-focus"
                placeholder="给你的愿望起个标题..."
                required
              >
              <p class="text-xs text-gray-500 mt-1">简洁明了地描述你的愿望</p>
            </div>
            
            <div class="space-y-1">
              <label for="wishPriority" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <i class="fa fa-flag text-primary/70 mr-2"></i>优先级
              </label>
              <select 
                id="wishPriority" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg form-focus"
              >
                <option value="low">低 - 可以慢慢实现</option>
                <option value="medium" selected>中 - 有计划地实现</option>
                <option value="high">高 - 优先实现</option>
              </select>
            </div>
          </div>
          
          <div class="space-y-1">
            <label for="wishDescription" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
              <i class="fa fa-align-left text-primary/70 mr-2"></i>详细描述
            </label>
            <textarea 
              id="wishDescription" 
              rows="4"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg form-focus"
              placeholder="详细描述一下这个愿望，为什么想要实现它，计划如何实现..."
              required
            ></textarea>
            <p class="text-xs text-gray-500 mt-1">越详细的描述，越容易找到实现的方向</p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-1">
              <label for="wishDate" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <i class="fa fa-calendar text-primary/70 mr-2"></i>期望实现日期
              </label>
              <input 
                type="date" 
                id="wishDate" 
                class="w-full px-4 py-3 border border-gray-300 rounded-lg form-focus"
              >
              <p class="text-xs text-gray-500 mt-1">为自己设定一个目标时间</p>
            </div>
            
            <div class="space-y-1">
              <label for="wishImageUpload" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <i class="fa fa-image text-primary/70 mr-2"></i>添加图片
              </label>
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary/50 transition-colors cursor-pointer bg-gray-50">
                <input 
                  type="file" 
                  id="wishImageUpload" 
                  accept="image/*"
                  class="hidden"
                >
                <label for="wishImageUpload" class="cursor-pointer">
                  <i class="fa fa-cloud-upload text-2xl text-gray-400 mb-2"></i>
                  <p class="text-sm text-gray-600">点击或拖拽图片到这里上传</p>
                  <p class="text-xs text-gray-500 mt-1">支持JPG、PNG格式，大小不超过5MB</p>
                </label>
              </div>
            </div>
          </div>
          
          <div class="text-right pt-2 border-t border-gray-100">
            <button 
              type="submit" 
              class="bg-primary hover:bg-primary/90 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center mx-auto md:mx-0 shadow-md hover:shadow-lg"
            >
              <i class="fa fa-paper-plane mr-2"></i> 保存愿望
            </button>
          </div>
        </form>
      </div>

      <!-- 愿望分类和筛选 -->
      <div class="flex flex-wrap justify-between items-center mb-8 gap-4 bg-white/70 backdrop-blur p-4 rounded-xl shadow-sm">
        <div class="flex flex-wrap gap-2">
          <button class="filter-btn active px-4 py-2 rounded-full bg-primary text-white text-sm font-medium transition-all" data-filter="all">
            全部愿望
          </button>
          <button class="filter-btn px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm font-medium transition-all" data-filter="pending">
            未完成
          </button>
          <button class="filter-btn px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm font-medium transition-all" data-filter="completed">
            已实现
          </button>
        </div>
        
        <div class="relative w-full md:w-auto">
          <input 
            type="text" 
            id="searchWishes" 
            placeholder="搜索愿望..." 
            class="pl-10 pr-4 py-2 rounded-full border border-gray-300 form-focus w-full md:w-64"
          >
          <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>

      <!-- 愿望列表 -->
      <div id="wishList" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 愿望卡片将通过JavaScript动态添加 -->
        <div class="col-span-full text-center py-16 text-gray-500 animate-fade-in">
          <i class="fa fa-book-open text-5xl mb-4 text-gray-300"></i>
          <p>还没有记录愿望哦，点击上方"添加新愿望"开始记录吧！</p>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="mt-16 text-center text-gray-500 text-sm py-6">
      <p>📖 愿望博客 | 记录每一个梦想，见证成长的足迹 📖</p>
    </footer>
  </div>

  <!-- 编辑模态框 (默认隐藏) -->
  <div id="editModal" class="fixed inset-0 bg-black/50 backdrop-blur z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto animate-slide-up">
      <div class="flex justify-between items-center mb-5">
        <h2 class="text-xl font-semibold text-primary flex items-center">
          <i class="fa fa-edit mr-2"></i> 编辑愿望
        </h2>
        <button id="closeEditModal" class="text-gray-500 hover:text-gray-700 transition-colors">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      
      <form id="editWishForm" class="space-y-5">
        <input type="hidden" id="editWishId">
        
        <div>
          <label for="editWishTitle" class="block text-sm font-medium text-gray-700 mb-1">愿望标题</label>
          <input 
            type="text" 
            id="editWishTitle" 
            class="w-full px-4 py-2 border border-gray-300 rounded-lg form-focus"
            required
          >
        </div>
        
        <div>
          <label for="editWishDescription" class="block text-sm font-medium text-gray-700 mb-1">详细描述</label>
          <textarea 
            id="editWishDescription" 
            rows="3"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg form-focus"
            required
          ></textarea>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <label for="editWishDate" class="block text-sm font-medium text-gray-700 mb-1">期望实现日期</label>
            <input 
              type="date" 
              id="editWishDate" 
              class="w-full px-4 py-2 border border-gray-300 rounded-lg form-focus"
            >
          </div>
          
          <div>
            <label for="editWishPriority" class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
            <select 
              id="editWishPriority" 
              class="w-full px-4 py-2 border border-gray-300 rounded-lg form-focus"
            >
              <option value="low">低 - 可以慢慢实现</option>
              <option value="medium">中 - 有计划地实现</option>
              <option value="high">高 - 优先实现</option>
            </select>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">图片</label>
          <div id="currentImageContainer" class="mb-3 hidden">
            <img id="currentImagePreview" src="" alt="当前图片" class="max-h-40 rounded-lg mb-2">
            <button type="button" id="removeImageBtn" class="text-sm text-red-500 hover:text-red-700 transition-colors">
              <i class="fa fa-trash-o mr-1"></i> 移除图片
            </button>
          </div>
          
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary/50 transition-colors cursor-pointer bg-gray-50">
            <input 
              type="file" 
              id="editWishImageUpload" 
              accept="image/*"
              class="hidden"
            >
            <label for="editWishImageUpload" class="cursor-pointer">
              <i class="fa fa-cloud-upload text-xl text-gray-400 mb-1"></i>
              <p class="text-sm text-gray-600">点击上传新图片</p>
            </label>
          </div>
        </div>
        
        <div class="pt-3 flex gap-3 justify-end">
          <button 
            type="button" 
            id="cancelEditBtn"
            class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium px-5 py-2 rounded-lg transition-all"
          >
            取消
          </button>
          <button 
            type="submit" 
            class="bg-primary hover:bg-primary/90 text-white font-medium px-5 py-2 rounded-lg transition-all"
          >
            保存修改
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- 预览图片模态框 (默认隐藏) -->
  <div id="imagePreviewModal" class="fixed inset-0 bg-black/80 backdrop-blur z-50 hidden items-center justify-center p-4">
    <button id="closeImageModal" class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300 transition-colors z-10">
      <i class="fa fa-times"></i>
    </button>
    <img id="modalImage" src="" alt="图片预览" class="max-h-[90vh] max-w-[90vw] object-contain">
  </div>

  <!-- 庆祝动画容器 (默认隐藏) -->
  <div id="celebration" class="fixed inset-0 pointer-events-none z-50 hidden">
    <!-- 庆祝元素将通过JavaScript动态添加 -->
  </div>

  <script>
    // 工具函数：处理图片
    const ImageUtils = {
      // 将图片文件转换为base64
      fileToBase64: (file) => {
        return new Promise((resolve, reject) => {
          // 检查文件大小（5MB限制）
          if (file.size > 5 * 1024 * 1024) {
            reject(new Error('图片大小不能超过5MB'));
            return;
          }
          
          // 检查文件类型
          if (!file.type.startsWith('image/')) {
            reject(new Error('请上传图片文件'));
            return;
          }
          
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve(reader.result);
          reader.onerror = error => reject(error);
        });
      }
    };

    // 愿望数据管理类
    class WishBlog {
      constructor() {
        // 从本地存储加载愿望数据
        this.wishes = JSON.parse(localStorage.getItem('wishBlog')) || [];
        this.wishListElement = document.getElementById('wishList');
        this.wishForm = document.getElementById('wishForm');
        this.wishFormContainer = document.getElementById('wishFormContainer');
        this.showAddWishBtn = document.getElementById('showAddWishBtn');
        this.hideAddWishBtn = document.getElementById('hideAddWishBtn');
        this.searchInput = document.getElementById('searchWishes');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.currentFilter = 'all';
        this.editModal = document.getElementById('editModal');
        this.editWishForm = document.getElementById('editWishForm');
        this.closeEditModal = document.getElementById('closeEditModal');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        this.removeImageBtn = document.getElementById('removeImageBtn');
        this.imagePreviewModal = document.getElementById('imagePreviewModal');
        this.modalImage = document.getElementById('modalImage');
        this.closeImageModal = document.getElementById('closeImageModal');
        
        // 初始化拖放上传功能
        this.initDragAndDrop();
        
        // 初始化事件监听和渲染
        this.initEventListeners();
        this.renderWishes();
      }

      // 初始化拖放上传功能
      initDragAndDrop() {
        const dropArea = document.querySelector('label[for="wishImageUpload"]').parentElement;
        const fileInput = document.getElementById('wishImageUpload');
        
        // 拖放事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, this.preventDefaults, false);
        });
        
        function highlight() {
          dropArea.classList.add('border-primary');
          dropArea.classList.add('bg-primary/5');
        }
        
        function unhighlight() {
          dropArea.classList.remove('border-primary');
          dropArea.classList.remove('bg-primary/5');
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
          dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        dropArea.addEventListener('drop', (e) => {
          const dt = e.dataTransfer;
          const file = dt.files[0];
          
          if (file) {
            fileInput.files = dt.files;
            // 显示预览
            this.showImagePreview(fileInput);
          }
        }, false);
      }
      
      // 阻止默认拖放行为
      preventsDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      // 显示图片预览
      showImagePreview(input) {
        if (input.files && input.files[0]) {
          const reader = new FileReader();
          reader.onload = (e) => {
            // 对于添加愿望表单
            if (input.id === 'wishImageUpload') {
              const dropArea = input.parentElement;
              dropArea.innerHTML = `
                <img src="${e.target.result}" alt="预览图" class="max-h-32 mx-auto rounded mb-2">
                <p class="text-sm text-gray-600">点击更换图片</p>
                <p class="text-xs text-gray-500 mt-1">${input.files[0].name}</p>
              `;
            }
          }
          reader.readAsDataURL(input.files[0]);
        }
      }

      // 初始化事件监听器
      initEventListeners() {
        // 添加愿望表单显示/隐藏
        this.showAddWishBtn.addEventListener('click', () => {
          this.wishFormContainer.classList.remove('hidden');
          this.showAddWishBtn.classList.add('hidden');
          // 滚动到表单位置
          this.wishFormContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
        
        this.hideAddWishBtn.addEventListener('click', () => {
          this.wishFormContainer.classList.add('hidden');
          this.showAddWishBtn.classList.remove('hidden');
        });

        // 表单提交事件
        this.wishForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.addWish();
        });

        // 图片上传预览
        document.getElementById('wishImageUpload').addEventListener('change', (e) => {
          this.showImagePreview(e.target);
        });
        
        document.getElementById('editWishImageUpload').addEventListener('change', (e) => {
          this.showImagePreview(e.target);
        });

        // 搜索事件
        this.searchInput.addEventListener('input', () => {
          this.renderWishes();
        });

        // 筛选按钮事件
        this.filterButtons.forEach(button => {
          button.addEventListener('click', () => {
            // 更新按钮样式
            this.filterButtons.forEach(btn => {
              btn.classList.remove('active', 'bg-primary', 'text-white');
              btn.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
            });
            button.classList.add('active', 'bg-primary', 'text-white');
            button.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
            
            // 更新筛选条件
            this.currentFilter = button.dataset.filter;
            this.renderWishes();
          });
        });

        // 编辑模态框事件
        this.closeEditModal.addEventListener('click', () => this.hideEditModal());
        this.cancelEditBtn.addEventListener('click', () => this.hideEditModal());
        this.editWishForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.saveEditedWish();
        });
        
        // 图片预览模态框事件
        this.closeImageModal.addEventListener('click', () => this.hideImageModal());
        this.imagePreviewModal.addEventListener('click', (e) => {
          if (e.target === this.imagePreviewModal) {
            this.hideImageModal();
          }
        });
        
        // 移除图片按钮事件
        this.removeImageBtn.addEventListener('click', () => {
          document.getElementById('currentImagePreview').src = '';
          document.getElementById('currentImageContainer').classList.add('hidden');
          document.getElementById('editWishImageUpload').value = '';
        });
      }
      
      // 显示图片预览模态框
      showImageModal(src) {
        this.modalImage.src = src;
        this.imagePreviewModal.classList.remove('hidden');
        this.imagePreviewModal.classList.add('flex');
        document.body.style.overflow = 'hidden';
      }
      
      // 隐藏图片预览模态框
      hideImageModal() {
        this.imagePreviewModal.classList.add('hidden');
        this.imagePreviewModal.classList.remove('flex');
        document.body.style.overflow = '';
      }

      // 添加新愿望
      async addWish() {
        const titleInput = document.getElementById('wishTitle');
        const descriptionInput = document.getElementById('wishDescription');
        const dateInput = document.getElementById('wishDate');
        const priorityInput = document.getElementById('wishPriority');
        const imageUploadInput = document.getElementById('wishImageUpload');

        // 验证必填字段
        if (!titleInput.value.trim() || !descriptionInput.value.trim()) {
          alert('请填写愿望标题和描述');
          return;
        }

        // 处理图片
        let imageSrc = '';
        if (imageUploadInput.files && imageUploadInput.files[0]) {
          try {
            // 处理上传的图片
            imageSrc = await ImageUtils.fileToBase64(imageUploadInput.files[0]);
          } catch (error) {
            alert(error.message);
            return;
          }
        }

        const newWish = {
          id: Date.now().toString(),
          title: titleInput.value.trim(),
          description: descriptionInput.value.trim(),
          date: dateInput.value,
          priority: priorityInput.value,
          image: imageSrc,
          completed: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        this.wishes.unshift(newWish); // 添加到数组开头
        this.saveWishes();
        this.renderWishes();

        // 重置表单
        this.wishForm.reset();
        imageUploadInput.value = ''; // 清除文件输入
        
        // 重置上传区域显示
        const dropArea = document.querySelector('label[for="wishImageUpload"]').parentElement;
        dropArea.innerHTML = `
          <label for="wishImageUpload" class="cursor-pointer">
            <i class="fa fa-cloud-upload text-2xl text-gray-400 mb-2"></i>
            <p class="text-sm text-gray-600">点击或拖拽图片到这里上传</p>
            <p class="text-xs text-gray-500 mt-1">支持JPG、PNG格式，大小不超过5MB</p>
          </label>
        `;
        
        // 隐藏表单，显示添加按钮
        this.wishFormContainer.classList.add('hidden');
        this.showAddWishBtn.classList.remove('hidden');
        
        // 添加成功动画
        this.showAddAnimation(newWish.id);
        
        // 滚动到新添加的愿望
        setTimeout(() => {
          const newWishElement = document.querySelector(`.edit-wish[data-id="${newWish.id}"]`).closest('article');
          if (newWishElement) {
            newWishElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }, 500);
      }

      // 切换愿望完成状态
      toggleWishCompletion(id) {
        const wish = this.wishes.find(w => w.id === id);
        if (wish) {
          wish.completed = !wish.completed;
          wish.updatedAt = new Date().toISOString();
          this.saveWishes();
          this.renderWishes();
          
          // 如果是完成状态，显示庆祝动画
          if (wish.completed) {
            this.showCelebration();
          }
        }
      }

      // 删除愿望
      deleteWish(id) {
        if (confirm('确定要删除这个愿望吗？此操作不可恢复。')) {
          this.wishes = this.wishes.filter(w => w.id !== id);
          this.saveWishes();
          this.renderWishes();
        }
      }

      // 打开编辑模态框
      openEditModal(id) {
        const wish = this.wishes.find(w => w.id === id);
        if (!wish) return;

        // 填充表单数据
        document.getElementById('editWishId').value = wish.id;
        document.getElementById('editWishTitle').value = wish.title;
        document.getElementById('editWishDescription').value = wish.description;
        document.getElementById('editWishDate').value = wish.date;
        document.getElementById('editWishPriority').value = wish.priority;
        
        // 处理图片预览
        const imageContainer = document.getElementById('currentImageContainer');
        const imagePreview = document.getElementById('currentImagePreview');
        
        if (wish.image) {
          imagePreview.src = wish.image;
          imageContainer.classList.remove('hidden');
        } else {
          imageContainer.classList.add('hidden');
        }
        
        // 清空文件上传输入
        document.getElementById('editWishImageUpload').value = '';

        // 显示模态框
        this.editModal.classList.remove('hidden');
        this.editModal.classList.add('flex');
        document.body.style.overflow = 'hidden'; // 防止背景滚动
      }

      // 隐藏编辑模态框
      hideEditModal() {
        this.editModal.classList.add('hidden');
        this.editModal.classList.remove('flex');
        document.body.style.overflow = ''; // 恢复背景滚动
      }

      // 保存编辑后的愿望
      async saveEditedWish() {
        const wishId = document.getElementById('editWishId').value;
        const wish = this.wishes.find(w => w.id === wishId);
        if (!wish) {
          this.hideEditModal();
          return;
        }

        // 获取表单数据
        const title = document.getElementById('editWishTitle').value.trim();
        const description = document.getElementById('editWishDescription').value.trim();
        const date = document.getElementById('editWishDate').value;
        const priority = document.getElementById('editWishPriority').value;
        const imageUpload = document.getElementById('editWishImageUpload');

        // 验证必填字段
        if (!title || !description) {
          alert('请填写愿望标题和描述');
          return;
        }

        // 处理图片
        let imageSrc = wish.image; // 默认使用现有图片
        if (imageUpload.files && imageUpload.files[0]) {
          try {
            // 处理新上传的图片
            imageSrc = await ImageUtils.fileToBase64(imageUpload.files[0]);
          } catch (error) {
            alert(error.message);
            return;
          }
        }

        // 更新愿望数据
        wish.title = title;
        wish.description = description;
        wish.date = date;
        wish.priority = priority;
        wish.image = imageSrc;
        wish.updatedAt = new Date().toISOString();

        this.saveWishes();
        this.renderWishes();
        this.hideEditModal();
      }

      // 保存愿望到本地存储
      saveWishes() {
        localStorage.setItem('wishBlog', JSON.stringify(this.wishes));
      }

      // 格式化日期显示
      formatDate(dateString) {
        if (!dateString) return '未设置';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }

      // 格式化相对时间（多久前）
      formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
          return '今天';
        } else if (diffDays === 1) {
          return '昨天';
        } else if (diffDays < 30) {
          return `${diffDays}天前`;
        } else {
          const diffMonths = Math.floor(diffDays / 30);
          return diffMonths === 1 ? '1个月前' : `${diffMonths}个月前`;
        }
      }

      // 渲染愿望列表
      renderWishes() {
        // 清空列表
        this.wishListElement.innerHTML = '';
        
        // 获取搜索词
        const searchTerm = this.searchInput.value.toLowerCase().trim();
        
        // 筛选愿望
        let filteredWishes = this.wishes;
        
        // 应用搜索筛选
        if (searchTerm) {
          filteredWishes = filteredWishes.filter(wish => 
            wish.title.toLowerCase().includes(searchTerm) || 
            wish.description.toLowerCase().includes(searchTerm)
          );
        }
        
        // 应用状态筛选
        if (this.currentFilter === 'pending') {
          filteredWishes = filteredWishes.filter(wish => !wish.completed);
        } else if (this.currentFilter === 'completed') {
          filteredWishes = filteredWishes.filter(wish => wish.completed);
        }
        
        // 显示筛选结果
        if (filteredWishes.length === 0) {
          this.wishListElement.innerHTML = `
            <div class="col-span-full text-center py-16 text-gray-500 animate-fade-in">
              <i class="fa fa-search text-5xl mb-4 text-gray-300"></i>
              <p>没有找到符合条件的愿望</p>
              <button id="scrollToAdd" class="mt-4 text-primary hover:text-primary/80 transition-colors flex items-center justify-center">
                <i class="fa fa-plus-circle mr-1"></i> 添加新愿望
              </button>
            </div>
          `;
          
          // 添加滚动到添加区域的事件
          document.getElementById('scrollToAdd').addEventListener('click', () => {
            this.showAddWishBtn.click();
          });
          
          return;
        }
        
        // 创建愿望卡片
        filteredWishes.forEach(wish => {
          const wishCard = document.createElement('article');
          
          // 根据优先级和完成状态设置不同的样式
          let priorityClass = '';
          if (wish.priority === 'high') {
            priorityClass = 'border-secondary';
          } else if (wish.priority === 'medium') {
            priorityClass = 'border-primary';
          } else {
            priorityClass = 'border-slate-400';
          }
          
          const completedClass = wish.completed ? 'opacity-90 bg-slate-50' : 'bg-white';
          
          // 设置卡片样式和内容
          wishCard.className = `${completedClass} rounded-xl overflow-hidden shadow-md border-t-4 ${priorityClass} card-hover animate-fade-in`;
          
          // 构建卡片HTML
          let cardHTML = `
            <div class="p-5">
              <div class="flex justify-between items-start mb-3">
                <span class="text-xs font-medium px-2.5 py-0.5 rounded-full ${
                  wish.priority === 'high' ? 'bg-pink-100 text-secondary' : 
                  wish.priority === 'medium' ? 'bg-indigo-100 text-primary' : 
                  'bg-slate-100 text-slate-700'
                }">
                  ${wish.priority === 'high' ? '高优先级' : 
                    wish.priority === 'medium' ? '中优先级' : '低优先级'}
                </span>
                <div class="flex space-x-1">
                  <button class="edit-wish p-1.5 rounded-full hover:bg-gray-100 transition-colors text-gray-500 hover:text-primary" data-id="${wish.id}" title="编辑">
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button class="delete-wish p-1.5 rounded-full hover:bg-gray-100 transition-colors text-gray-500 hover:text-red-500" data-id="${wish.id}" title="删除">
                    <i class="fa fa-trash-o"></i>
                  </button>
                </div>
              </div>
              
              <h3 class="text-xl font-semibold mb-2 ${wish.completed ? 'line-through text-gray-500' : ''}">${wish.title}</h3>
              
              <p class="text-gray-600 text-sm mb-4 line-clamp-3">${wish.description}</p>
          `;
          
          // 添加图片（如果有）
          if (wish.image) {
            cardHTML += `
              <div class="mb-4 rounded-lg overflow-hidden relative group">
                <img src="${wish.image}" alt="${wish.title}" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105 cursor-pointer image-preview" data-src="${wish.image}">
                <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <i class="fa fa-search-plus text-white text-2xl"></i>
                </div>
              </div>
            `;
          }
          
          // 添加底部信息
          cardHTML += `
              <div class="flex flex-wrap justify-between items-center gap-2 mt-3 pt-3 border-t border-gray-100">
                <div class="text-xs text-gray-500">
                  <i class="fa fa-calendar-o mr-1"></i> 目标: ${this.formatDate(wish.date)}
                </div>
                <div class="text-xs text-gray-500">
                  <i class="fa fa-clock-o mr-1"></i> ${this.formatRelativeTime(wish.createdAt)}
                </div>
                <button class="toggle-complete mt-2 px-3 py-1 text-sm rounded-full transition-all ${
                  wish.completed ? 'bg-gray-200 text-gray-700 hover:bg-gray-300' : 'bg-accent text-white hover:bg-accent/90'
                }" data-id="${wish.id}">
                  <i class="fa ${wish.completed ? 'fa-undo mr-1' : 'fa-check mr-1'}"></i>
                  ${wish.completed ? '标记为未完成' : '标记为已实现'}
                </button>
              </div>
            </div>
          `;
          
          wishCard.innerHTML = cardHTML;
          this.wishListElement.appendChild(wishCard);
        });
        
        // 添加事件监听器到新创建的按钮
        this.addWishCardEventListeners();
      }

      // 为愿望卡片添加事件监听器
      addWishCardEventListeners() {
        // 完成按钮事件
        document.querySelectorAll('.toggle-complete').forEach(button => {
          button.addEventListener('click', () => {
            this.toggleWishCompletion(button.dataset.id);
          });
        });
        
        // 编辑按钮事件
        document.querySelectorAll('.edit-wish').forEach(button => {
          button.addEventListener('click', () => {
            this.openEditModal(button.dataset.id);
          });
        });
        
        // 删除按钮事件
        document.querySelectorAll('.delete-wish').forEach(button => {
          button.addEventListener('click', () => {
            this.deleteWish(button.dataset.id);
          });
        });
        
        // 图片预览事件 - 只绑定在图片元素上
        document.querySelectorAll('.image-preview').forEach(image => {
          image.addEventListener('click', (e) => {
            e.stopPropagation(); // 防止事件冒泡
            this.showImageModal(image.dataset.src);
          });
        });
      }

      // 显示添加愿望的动画
      showAddAnimation(wishId) {
        const wishCard = document.querySelector(`.edit-wish[data-id="${wishId}"]`).closest('article');
        if (wishCard) {
          wishCard.classList.add('ring-4', 'ring-primary/30');
          setTimeout(() => {
            wishCard.classList.remove('ring-4', 'ring-primary/30');
          }, 1500);
        }
      }

      // 显示庆祝动画
      showCelebration() {
        const celebrationContainer = document.getElementById('celebration');
        celebrationContainer.classList.remove('hidden');
        celebrationContainer.innerHTML = '';
        
        // 创建60个随机位置的庆祝元素
        for (let i = 0; i < 60; i++) {
          const celebrationElement = document.createElement('div');
          const size = Math.random() * 20 + 10;
          const color = ['text-yellow-400', 'text-primary', 'text-secondary', 'text-accent'][Math.floor(Math.random() * 4)];
          const icon = ['fa-star', 'fa-heart', 'fa-gift', 'fa-trophy', 'fa-smile-o'][Math.floor(Math.random() * 5)];
          
          celebrationElement.className = `absolute ${color} animate-float`;
          celebrationElement.style.left = `${Math.random() * 100}%`;
          celebrationElement.style.top = `${Math.random() * 100}%`;
          celebrationElement.style.fontSize = `${size}px`;
          celebrationElement.style.animationDelay = `${Math.random() * 2}s`;
          celebrationElement.innerHTML = `<i class="fa ${icon}"></i>`;
          
          celebrationContainer.appendChild(celebrationElement);
        }
        
        // 3秒后隐藏庆祝动画
        setTimeout(() => {
          celebrationContainer.classList.add('hidden');
          celebrationContainer.innerHTML = '';
        }, 3000);
      }
    }

    // 页面加载完成后初始化愿望博客
    document.addEventListener('DOMContentLoaded', () => {
      new WishBlog();
    });
  </script>
</body>
</html>
