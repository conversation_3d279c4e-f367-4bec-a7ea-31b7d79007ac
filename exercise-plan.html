<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个人科学运动计划 - 生活NOTE</title>
  <!-- 引入统一样式 -->
  <link rel="stylesheet" href="common-styles.css">
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <!-- 引入通用工具 -->
  <script src="common-utils.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#10B981',
            accent: '#F59E0B',
            light: '#F3F4F6',
            dark: '#1F2937'
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      .editable:focus {
        outline: 2px solid #3B82F6;
        border-radius: 0.375rem;
      }
    }
  </style>
</head>
<body class="bg-gray-50 font-sans text-dark">
    <!-- 引入统一导航栏 -->
    <div id="navbar-container"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前页面名称（用于高亮当前导航项）
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    // 高亮当前页面导航链接
    const navLinks = document.querySelectorAll(`.nav-link[data-page="${currentPage}"], .mobile-nav-link[data-page="${currentPage}"]`);
    navLinks.forEach(link => {
        link.classList.add('active');
    });
    
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
    
    // 滚动时导航栏样式变化（加深阴影）
    window.addEventListener('scroll', function() {
        const nav = document.querySelector('nav');
        if (nav) {
            if (window.scrollY > 10) {
                nav.classList.add('shadow-lg');
            } else {
                nav.classList.remove('shadow-lg');
            }
        }
    });
});
</script>
  <!-- 主要内容 -->
  <main class="container mx-auto px-4 py-8" style="padding-top: 5rem;">
    <!-- 介绍卡片 -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8 card-hover">
      <h2 class="text-xl font-semibold mb-4 text-primary border-b pb-2">
        <i class="fa fa-info-circle mr-2"></i>
        <span contenteditable="true" class="editable">计划说明</span>
      </h2>
      <p class="text-gray-700 mb-4 editable" contenteditable="true">
        本计划基于世界卫生组织（WHO）运动建议制定，每周运动总时长约150-180分钟，适合成年人独自完成。所有内容均可点击直接编辑，可根据个人体能调整。
      </p>
      <div class="grid md:grid-cols-3 gap-4 mt-6">
        <div class="bg-light p-4 rounded-lg">
          <div class="flex items-center text-secondary mb-2">
            <i class="fa fa-bolt text-xl mr-2"></i>
            <h3 class="font-medium">有氧运动为主</h3>
          </div>
          <p class="text-sm text-gray-600 editable" contenteditable="true">每周累计150分钟中等强度有氧，提升心肺功能</p>
        </div>
        <div class="bg-light p-4 rounded-lg">
          <div class="flex items-center text-secondary mb-2">
            <i class="fa fa-dumbbell text-xl mr-2"></i>
            <h3 class="font-medium">力量训练为辅</h3>
          </div>
          <p class="text-sm text-gray-600 editable" contenteditable="true">每周2-3次力量训练，覆盖主要肌群</p>
        </div>
        <div class="bg-light p-4 rounded-lg">
          <div class="flex items-center text-secondary mb-2">
            <i class="fa fa-leaf text-xl mr-2"></i>
            <h3 class="font-medium">动静结合</h3>
          </div>
          <p class="text-sm text-gray-600 editable" contenteditable="true">每天预留拉伸时间，给身体充分恢复空间</p>
        </div>
      </div>
    </div>

    <!-- 运动计划表格 -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8 overflow-x-auto">
      <h2 class="text-xl font-semibold mb-6 text-primary border-b pb-2">
        <i class="fa fa-calendar mr-2"></i>
        <span contenteditable="true" class="editable">一周运动计划详情</span>
      </h2>
      
      <table class="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">时间</th>
            <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">运动类型</th>
            <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">具体内容</th>
            <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">强度/时长</th>
            <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tr-lg">动作要点</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <!-- 周一 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周一</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">中等强度有氧</td>
            <td class="px-4 py-4 editable" contenteditable="true">户外快走/慢跑（推荐公园、河边步道）</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">30-40分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">中等强度：心率达到（220-年龄）×60%-70%，能说话但不能唱歌；慢跑时步幅适中，落地轻缓。</td>
          </tr>
          <!-- 周二 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周二</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">上肢力量训练</td>
            <td class="px-4 py-4 editable" contenteditable="true">居家无器械训练：<br>1. 俯卧撑（跪姿/标准）×3组×10-15次<br>2. 靠墙倒立撑（新手可做靠墙推）×3组×8-12次<br>3. 哑铃弯举（可用瓶装水替代）×3组×12次/侧</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">20-25分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">发力时收紧核心，避免耸肩；俯卧撑下降时胸部接近地面，肘部与身体成45°角。</td>
          </tr>
          <!-- 周三 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周三</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">轻度活动/休息</td>
            <td class="px-4 py-4 editable" contenteditable="true">选项1：散步20分钟（饭后走最佳）<br>选项2：瑜伽基础动作（猫牛式、婴儿式）×15分钟</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">低强度</td>
            <td class="px-4 py-4 editable" contenteditable="true">以放松为主，缓解前两日运动疲劳，促进血液循环。</td>
          </tr>
          <!-- 周四 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周四</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">中等强度有氧</td>
            <td class="px-4 py-4 editable" contenteditable="true">跳绳（无绳/有绳均可）+ 间歇走<br>模式：跳1分钟（中等速度）+ 走30秒，重复8-10组</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">30分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">跳绳落地时膝盖微屈缓冲，避免踮脚；间歇走时调整呼吸，保持节奏。</td>
          </tr>
          <!-- 周五 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周五</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">下肢+核心训练</td>
            <td class="px-4 py-4 editable" contenteditable="true">1. 徒手深蹲×3组×15-20次<br>2. 箭步蹲×3组×10次/腿<br>3. 平板支撑×3组×30-60秒<br>4. 卷腹×3组×15次</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">25-30分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">深蹲时膝盖与脚尖方向一致，不超过脚尖；平板支撑臀部不塌陷、不撅起，核心收紧。</td>
          </tr>
          <!-- 周六 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周六</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">高强度有氧</td>
            <td class="px-4 py-4 editable" contenteditable="true">跑步间歇训练：<br>快跑1分钟（心率达最大心率80%）+ 慢走2分钟，重复6-8组</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">25-30分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">高强度阶段全力冲刺，慢走时充分换气；结束后慢走5分钟降温。</td>
          </tr>
          <!-- 周日 -->
          <tr class="hover:bg-gray-50 transition">
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">周日</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">全身拉伸放松</td>
            <td class="px-4 py-4 editable" contenteditable="true">1. 静态拉伸：大腿前侧/后侧、背部、肩部各保持20-30秒<br>2. 泡沫轴放松（可选）：滚动大腿、小腿肌肉</td>
            <td class="px-4 py-4 whitespace-nowrap editable" contenteditable="true">15-20分钟</td>
            <td class="px-4 py-4 editable" contenteditable="true">拉伸时缓慢发力，不追求痛感，以"轻微牵拉感"为宜；泡沫轴滚动速度缓慢，重点放松紧张部位。</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 调整建议和注意事项 -->
    <div class="grid md:grid-cols-2 gap-6 mb-8">
      <!-- 调整建议 -->
      <div class="bg-white rounded-xl shadow-md p-6 card-hover">
        <h2 class="text-xl font-semibold mb-4 text-accent border-b pb-2">
          <i class="fa fa-sliders mr-2"></i>
          <span contenteditable="true" class="editable">灵活调整建议</span>
        </h2>
        <ul class="space-y-3 text-gray-700">
          <li class="flex">
            <i class="fa fa-user-o text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>新手入门：</strong>若无法完成标准动作，可降低难度（如俯卧撑改跪姿、深蹲手扶墙），有氧时长先从20分钟起步，每周增加5-10分钟。</span>
          </li>
          <li class="flex">
            <i class="fa fa-level-up text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>进阶提升：</strong>高强度有氧可增加爬坡跑、波比跳；力量训练加入负重（如背包放书增加重量），每组次数提升至15-20次。</span>
          </li>
          <li class="flex">
            <i class="fa fa-cloud text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>天气限制：</strong>雨天可替换为室内运动，如爬楼梯（10层×5组）、居家跳绳、跟着视频做有氧操（推荐帕梅拉低冲击系列）。</span>
          </li>
        </ul>
      </div>

      <!-- 注意事项 -->
      <div class="bg-white rounded-xl shadow-md p-6 card-hover">
        <h2 class="text-xl font-semibold mb-4 text-accent border-b pb-2">
          <i class="fa fa-exclamation-circle mr-2"></i>
          <span contenteditable="true" class="editable">注意事项</span>
        </h2>
        <ul class="space-y-3 text-gray-700">
          <li class="flex">
            <i class="fa fa-fire text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>热身与冷却：</strong>每次运动前5分钟动态热身（高抬腿、开合跳），运动后5分钟静态拉伸，降低受伤风险。</span>
          </li>
          <li class="flex">
            <i class="fa fa-heartbeat text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>监测身体信号：</strong>若出现关节疼痛、头晕、心悸，立即停止运动；肌肉酸痛属正常现象，可通过热敷、按摩缓解。</span>
          </li>
          <li class="flex">
            <i class="fa fa-cutlery text-accent mt-1 mr-2"></i>
            <span contenteditable="true" class="editable"><strong>配合饮食与休息：</strong>运动后30分钟补充蛋白质（如鸡蛋、牛奶）和碳水（如全麦面包），保证每天7-8小时睡眠，促进肌肉修复。</span>
          </li>
        </ul>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-dark text-white py-6">
    <div class="container mx-auto px-4 text-center">
      <p contenteditable="true" class="editable">本计划可根据个人实际情况灵活调整，坚持4周后可根据体能提升进一步优化。</p>
      <p class="mt-2 text-gray-400">© 2023 个人运动计划 | 科学运动，健康生活</p>
    </div>
  </footer>

  <!-- 提示弹窗 -->
  <div id="toast" class="fixed bottom-5 right-5 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
    <i class="fa fa-check-circle mr-2"></i>
    <span>修改已保存！（注：页面刷新后需重新保存）</span>
  </div>

  <script>
    // 保存提示功能
    document.getElementById('save提示').addEventListener('click', function() {
      const toast = document.getElementById('toast');
      // 显示提示
      toast.classList.remove('translate-y-20', 'opacity-0');
      toast.classList.add('translate-y-0', 'opacity-100');
      
      // 3秒后隐藏
      setTimeout(() => {
        toast.classList.remove('translate-y-0', 'opacity-100');
        toast.classList.add('translate-y-20', 'opacity-0');
      }, 3000);
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
          behavior: 'smooth'
        });
      });
    });
  </script>

  <!-- 加载统一导航栏 -->
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          // 加载导航栏
          fetch('navbar.html')
              .then(response => response.text())
              .then(html => {
                  document.getElementById('navbar-container').innerHTML = html;

                  // 执行导航栏中的脚本
                  const scripts = document.getElementById('navbar-container').querySelectorAll('script');
                  scripts.forEach(script => {
                      const newScript = document.createElement('script');
                      if (script.src) {
                          newScript.src = script.src;
                      } else {
                          newScript.textContent = script.textContent;
                      }
                      document.head.appendChild(newScript);
                  });
              })
              .catch(error => {
                  console.error('加载导航栏失败:', error);
              });
      });
  </script>
</body>
</html>