/* 统一的样式系统 - 生活NOTE */

/* CSS变量定义 */
:root {
    --primary: #4F46E5;
    --secondary: #10B981;
    --accent: #F59E0B;
    --light: #F5F7FF;
    --dark: #1E293B;
    --success: #10B981;
    --study: #3B82F6;
    --work: #10B981;
    --life: #F59E0B;
    --improvement: #A78BFA;
    --danger: #EF4444;
}

/* 基础样式重置 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #F9FAFB;
    color: var(--dark);
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: #4338CA;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: #059669;
    transform: translateY(-1px);
}

.btn-light {
    background-color: var(--light);
    color: var(--primary);
}

.btn-light:hover {
    background-color: #E0E7FF;
    transform: translateY(-1px);
}

.btn-effect {
    transform: translateY(0);
    transition: transform 0.2s ease;
}

.btn-effect:hover {
    transform: translateY(-2px);
}

/* 输入框样式 */
.input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #D1D5DB;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 通知样式 */
.notification {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateY(2.5rem);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification-success {
    background-color: var(--success);
    color: white;
}

.notification-error {
    background-color: var(--danger);
    color: white;
}

.notification-info {
    background-color: var(--primary);
    color: white;
}

/* 任务相关样式 */
.task-complete {
    text-decoration: line-through;
    color: #9CA3AF;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #F3F4F6;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.task-item:hover {
    background-color: #F9FAFB;
}

/* 类别颜色 */
.category-study {
    background-color: var(--study);
}

.category-work {
    background-color: var(--work);
}

.category-life {
    background-color: var(--life);
}

.category-improvement {
    background-color: var(--improvement);
}

/* 进度条样式 */
.progress-ring {
    transition: all 1s ease-out;
}

/* 反思卡片样式 */
.reflection-card {
    background: white;
    border: 1px solid #F3F4F6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.reflection-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 日历样式 */
.calendar-day {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    background-color: var(--light);
}

.calendar-day-selected {
    background-color: var(--primary);
    color: white;
}

.calendar-day-today {
    ring: 2px solid var(--primary);
    ring-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.625rem 0.875rem;
        font-size: 0.8rem;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }

.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
