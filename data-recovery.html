<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生活NOTE - 数据恢复工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-600">
            <i class="fa fa-database mr-2"></i>生活NOTE 数据恢复工具
        </h1>

        <!-- 数据检查区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 text-green-600">
                <i class="fa fa-search mr-2"></i>数据完整性检查
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <button onclick="checkAllData()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <i class="fa fa-check mr-2"></i>检查所有数据
                </button>
                <button onclick="showRawStorage()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    <i class="fa fa-eye mr-2"></i>查看原始存储
                </button>
            </div>
            <div id="check-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>

        <!-- 数据恢复区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 text-orange-600">
                <i class="fa fa-wrench mr-2"></i>数据恢复工具
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <button onclick="createSampleExpenseData()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    <i class="fa fa-plus mr-2"></i>创建示例支出数据
                </button>
                <button onclick="createSamplePlanData()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    <i class="fa fa-plus mr-2"></i>创建示例计划数据
                </button>
                <button onclick="clearAllData()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <i class="fa fa-trash mr-2"></i>清空所有数据
                </button>
            </div>
            <div id="recovery-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>

        <!-- 数据导入导出区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 text-purple-600">
                <i class="fa fa-exchange mr-2"></i>数据导入导出
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <button onclick="exportAllData()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <i class="fa fa-download mr-2"></i>导出所有数据
                </button>
                <div>
                    <input type="file" id="import-file" accept=".json" class="hidden">
                    <button onclick="document.getElementById('import-file').click()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        <i class="fa fa-upload mr-2"></i>导入数据文件
                    </button>
                </div>
            </div>
            <div id="import-export-results" class="bg-gray-100 p-4 rounded text-sm"></div>
        </div>

        <!-- 快速导航 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold mb-4 text-indigo-600">
                <i class="fa fa-compass mr-2"></i>快速导航
            </h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="main-plan.html" class="bg-indigo-500 text-white px-4 py-2 rounded text-center hover:bg-indigo-600">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="fruit-plan.html" class="bg-green-500 text-white px-4 py-2 rounded text-center hover:bg-green-600">
                    <i class="fa fa-apple mr-2"></i>水果计划
                </a>
                <a href="exercise-plan.html" class="bg-red-500 text-white px-4 py-2 rounded text-center hover:bg-red-600">
                    <i class="fa fa-heartbeat mr-2"></i>锻炼计划
                </a>
                <a href="expense-tracker.html" class="bg-yellow-500 text-white px-4 py-2 rounded text-center hover:bg-yellow-600">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
            </div>
        </div>
    </div>

    <script>
        // 检查所有数据
        function checkAllData() {
            const results = document.getElementById('check-results');
            let output = '<strong>数据检查结果：</strong><br><br>';
            
            // 检查支出记录数据
            const expenses = localStorage.getItem('expenses');
            if (expenses) {
                try {
                    const data = JSON.parse(expenses);
                    output += `✅ 支出记录数据：存在，共 ${data.length} 条记录<br>`;
                } catch (e) {
                    output += `❌ 支出记录数据：格式错误<br>`;
                }
            } else {
                output += `❌ 支出记录数据：不存在<br>`;
            }
            
            // 检查主计划数据
            const dailyPlanner = localStorage.getItem('dailyPlanner');
            if (dailyPlanner) {
                try {
                    const data = JSON.parse(dailyPlanner);
                    const taskDays = Object.keys(data.tasks || {}).length;
                    const reflectionDays = Object.keys(data.reflections || {}).length;
                    output += `✅ 主计划数据：存在，${taskDays} 天任务，${reflectionDays} 天感想<br>`;
                } catch (e) {
                    output += `❌ 主计划数据：格式错误<br>`;
                }
            } else {
                output += `❌ 主计划数据：不存在<br>`;
            }
            
            // 检查水果计划数据
            const fruitPlan = localStorage.getItem('fruitPlanData');
            if (fruitPlan) {
                output += `✅ 水果计划数据：存在<br>`;
            } else {
                output += `❌ 水果计划数据：不存在<br>`;
            }
            
            // 检查周信息
            const weekInfo = localStorage.getItem('currentWeekInfo');
            if (weekInfo) {
                output += `✅ 当前周信息：存在<br>`;
            } else {
                output += `❌ 当前周信息：不存在<br>`;
            }
            
            results.innerHTML = output;
        }

        // 显示原始存储
        function showRawStorage() {
            const results = document.getElementById('check-results');
            let output = '<strong>本地存储原始数据：</strong><br><br>';
            
            const keys = Object.keys(localStorage);
            if (keys.length === 0) {
                output += '本地存储为空';
            } else {
                keys.forEach(key => {
                    const value = localStorage.getItem(key);
                    output += `<strong>${key}:</strong> ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}<br><br>`;
                });
            }
            
            results.innerHTML = output;
        }

        // 创建示例支出数据
        function createSampleExpenseData() {
            const sampleExpenses = [
                {
                    id: Date.now(),
                    amount: 25.50,
                    category: '餐饮',
                    description: '午餐',
                    date: new Date().toISOString().split('T')[0]
                },
                {
                    id: Date.now() + 1,
                    amount: 120.00,
                    category: '交通',
                    description: '地铁月卡',
                    date: new Date().toISOString().split('T')[0]
                }
            ];
            
            localStorage.setItem('expenses', JSON.stringify(sampleExpenses));
            document.getElementById('recovery-results').innerHTML = '✅ 已创建示例支出数据（2条记录）';
        }

        // 创建示例计划数据
        function createSamplePlanData() {
            const today = new Date().toISOString().split('T')[0];
            const samplePlanData = {
                tasks: {
                    [today]: [
                        {
                            id: Date.now().toString(),
                            text: '示例任务1',
                            completed: false,
                            category: 'study'
                        },
                        {
                            id: (Date.now() + 1).toString(),
                            text: '示例任务2',
                            completed: true,
                            category: 'life'
                        }
                    ]
                },
                improvements: {},
                reflections: {
                    [today]: [
                        {
                            id: Date.now().toString(),
                            text: '今天是美好的一天！',
                            timestamp: Date.now()
                        }
                    ]
                },
                ratings: {
                    [today]: 4
                },
                history: {}
            };
            
            localStorage.setItem('dailyPlanner', JSON.stringify(samplePlanData));
            document.getElementById('recovery-results').innerHTML = '✅ 已创建示例主计划数据';
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销！')) {
                localStorage.clear();
                document.getElementById('recovery-results').innerHTML = '⚠️ 所有数据已清空';
            }
        }

        // 导出所有数据
        function exportAllData() {
            const allData = {};
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    try {
                        allData[key] = JSON.parse(localStorage.getItem(key));
                    } catch (e) {
                        allData[key] = localStorage.getItem(key);
                    }
                }
            }
            
            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `生活NOTE_完整备份_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            document.getElementById('import-export-results').innerHTML = '✅ 数据已导出到下载文件夹';
        }

        // 导入数据文件
        document.getElementById('import-file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    // 恢复数据到localStorage
                    for (let key in importedData) {
                        localStorage.setItem(key, JSON.stringify(importedData[key]));
                    }
                    
                    document.getElementById('import-export-results').innerHTML = '✅ 数据导入成功！';
                } catch (error) {
                    document.getElementById('import-export-results').innerHTML = '❌ 数据导入失败：文件格式错误';
                }
            };
            reader.readAsText(file);
        });

        // 页面加载时自动检查数据
        window.addEventListener('load', function() {
            checkAllData();
        });
    </script>
</body>
</html>
