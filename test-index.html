<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生活NOTE - 测试页面</title>
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="common-styles.css">
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入通用工具 -->
    <script src="common-utils.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        light: '#F5F7FF',
                        dark: '#1E293B'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans text-dark">
    <!-- 引入统一导航栏 -->
    <div id="navbar-container"></div>

    <main class="container mx-auto px-4 py-8" style="padding-top: 5rem;">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-primary mb-4">生活NOTE 系统测试</h1>
            <p class="text-gray-600 text-lg">验证所有页面功能和数据完整性</p>
        </div>

        <!-- 功能测试区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 导航测试 -->
            <div class="card">
                <h3 class="text-lg font-bold text-primary mb-4">
                    <i class="fa fa-compass mr-2"></i>导航测试
                </h3>
                <div class="space-y-3">
                    <a href="main-plan.html" class="btn btn-primary w-full">
                        <i class="fa fa-home mr-2"></i>主计划页面
                    </a>
                    <a href="fruit-plan.html" class="btn btn-secondary w-full">
                        <i class="fa fa-apple mr-2"></i>水果计划页面
                    </a>
                    <a href="exercise-plan.html" class="btn btn-light w-full">
                        <i class="fa fa-heartbeat mr-2"></i>锻炼计划页面
                    </a>
                    <a href="expense-tracker.html" class="btn btn-light w-full">
                        <i class="fa fa-money mr-2"></i>支出记录页面
                    </a>
                </div>
            </div>

            <!-- 通知系统测试 -->
            <div class="card">
                <h3 class="text-lg font-bold text-primary mb-4">
                    <i class="fa fa-bell mr-2"></i>通知系统测试
                </h3>
                <div class="space-y-3">
                    <button onclick="notify.show('成功消息测试', 'success')" class="btn btn-secondary w-full">
                        成功通知
                    </button>
                    <button onclick="notify.show('错误消息测试', 'error')" class="btn w-full" style="background-color: #EF4444; color: white;">
                        错误通知
                    </button>
                    <button onclick="notify.show('信息消息测试', 'info')" class="btn btn-primary w-full">
                        信息通知
                    </button>
                    <button onclick="notify.show('警告消息测试', 'warning')" class="btn w-full" style="background-color: #F59E0B; color: white;">
                        警告通知
                    </button>
                </div>
            </div>

            <!-- 数据存储测试 -->
            <div class="card">
                <h3 class="text-lg font-bold text-primary mb-4">
                    <i class="fa fa-database mr-2"></i>数据存储测试
                </h3>
                <div class="space-y-3">
                    <button onclick="testStorage()" class="btn btn-primary w-full">
                        测试存储功能
                    </button>
                    <button onclick="checkDataIntegrity()" class="btn btn-secondary w-full">
                        检查数据完整性
                    </button>
                    <button onclick="clearTestData()" class="btn w-full" style="background-color: #EF4444; color: white;">
                        清除测试数据
                    </button>
                </div>
                <div id="storage-status" class="mt-3 text-sm text-gray-600"></div>
            </div>
        </div>

        <!-- 数据完整性检查结果 -->
        <div class="card">
            <h3 class="text-lg font-bold text-primary mb-4">
                <i class="fa fa-check-circle mr-2"></i>数据完整性检查结果
            </h3>
            <div id="integrity-results" class="space-y-2">
                <p class="text-gray-600">点击"检查数据完整性"按钮开始检查...</p>
            </div>
        </div>
    </main>

    <!-- 加载统一导航栏 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载导航栏
            fetch('navbar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('navbar-container').innerHTML = html;
                    
                    // 执行导航栏中的脚本
                    const scripts = document.getElementById('navbar-container').querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                    });
                })
                .catch(error => {
                    console.error('加载导航栏失败:', error);
                });
        });

        // 测试存储功能
        function testStorage() {
            const testData = {
                timestamp: new Date().toISOString(),
                testValue: Math.random()
            };
            
            const success = StorageUtils.set('test-data', testData);
            const retrieved = StorageUtils.get('test-data');
            
            const statusEl = document.getElementById('storage-status');
            if (success && retrieved && retrieved.testValue === testData.testValue) {
                statusEl.innerHTML = '<span class="text-green-600">✓ 存储功能正常</span>';
                notify.show('存储功能测试通过', 'success');
            } else {
                statusEl.innerHTML = '<span class="text-red-600">✗ 存储功能异常</span>';
                notify.show('存储功能测试失败', 'error');
            }
        }

        // 检查数据完整性
        function checkDataIntegrity() {
            const resultsEl = document.getElementById('integrity-results');
            const results = [];
            
            // 检查主计划数据
            const dailyPlannerData = StorageUtils.get('dailyPlanner');
            if (dailyPlannerData) {
                results.push(`✓ 主计划数据存在 (${Object.keys(dailyPlannerData.tasks || {}).length} 个日期)`);
                results.push(`✓ 改进项数据存在 (${Object.keys(dailyPlannerData.improvements || {}).length} 个日期)`);
                results.push(`✓ 感想数据存在 (${Object.keys(dailyPlannerData.reflections || {}).length} 个日期)`);
                results.push(`✓ 评分数据存在 (${Object.keys(dailyPlannerData.ratings || {}).length} 个日期)`);
                results.push(`✓ 历史数据存在 (${Object.keys(dailyPlannerData.history || {}).length} 个日期)`);
            } else {
                results.push('⚠ 主计划数据不存在');
            }
            
            // 检查水果计划数据
            const fruitPlanData = StorageUtils.get('fruitPlanData');
            if (fruitPlanData) {
                results.push(`✓ 水果计划数据存在`);
            } else {
                results.push('⚠ 水果计划数据不存在');
            }
            
            const currentWeekInfo = StorageUtils.get('currentWeekInfo');
            if (currentWeekInfo) {
                results.push(`✓ 当前周信息存在 (第${currentWeekInfo.week}周)`);
            } else {
                results.push('⚠ 当前周信息不存在');
            }
            
            resultsEl.innerHTML = results.map(result => `<p class="text-sm">${result}</p>`).join('');
            notify.show('数据完整性检查完成', 'info');
        }

        // 清除测试数据
        function clearTestData() {
            if (confirm('确定要清除测试数据吗？这不会影响您的实际数据。')) {
                StorageUtils.remove('test-data');
                document.getElementById('storage-status').innerHTML = '<span class="text-gray-600">测试数据已清除</span>';
                notify.show('测试数据已清除', 'success');
            }
        }
    </script>
</body>
</html>
