<!-- navbar.html -->
<nav class="bg-white shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
    <!-- 去掉容器的max-width限制，确保全屏 -->
    <div class="container mx-auto px-4">
        <div class="flex justify-between h-16">
            <!-- 左侧Logo/标题 -->
            <div class="flex items-center">
                <a href="main-plan.html" class="flex items-center">
                    <i class="fa fa-calendar-check-o text-primary text-2xl mr-2"></i>
                    <span class="font-bold text-lg text-gray-800">计划管理中心</span>
                </a>
            </div>
            
            <!-- 右侧导航链接 - 桌面端 -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="main-plan.html" class="nav-link" data-page="main-plan">
                    <i class="fa fa-home mr-1"></i> 主计划
                </a>
                <a href="exercise-plan.html" class="nav-link" data-page="exercise-plan">
                    <i class="fa fa-heartbeat mr-1"></i> 锻炼计划
                </a>
                <a href="fruit-plan.html" class="nav-link" data-page="fruit-plan">
                    <i class="fa fa-apple mr-1"></i> 水果计划
                </a>
            </div>
            
            <!-- 移动端菜单按钮 -->
            <div class="md:hidden flex items-center">
                <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-700">
                    <i class="fa fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 移动端导航菜单（全屏宽度） -->
    <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="main-plan.html" class="mobile-nav-link block px-3 py-2" data-page="main-plan">
                <i class="fa fa-home mr-1"></i> 主计划
            </a>
            <a href="exercise-plan.html" class="mobile-nav-link block px-3 py-2" data-page="exercise-plan">
                <i class="fa fa-heartbeat mr-1"></i> 锻炼计划
            </a>
            <a href="fruit-plan.html" class="mobile-nav-link block px-3 py-2" data-page="fruit-plan">
                <i class="fa fa-apple mr-1"></i> 水果计划
            </a>
        </div>
    </div>
</nav>

<style>
/* 导航链接样式 */
.nav-link {
    color: #6B7280;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 0.5rem 0;
    position: relative;
}

.nav-link:hover {
    color: #4F46E5;
}

.nav-link.active {
    color: #4F46E5;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4F46E5;
}

/* 移动端导航链接 */
.mobile-nav-link {
    color: #6B7280;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.mobile-nav-link:hover, .mobile-nav-link.active {
    color: #4F46E5;
    background-color: #F5F7FF;
}

/* 关键：为所有页面添加顶部间距，避免内容被导航栏遮挡 */
body {
    margin: 0;
    padding-top: 4rem; /* 与导航栏高度一致 */
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前页面名称（用于高亮当前导航项）
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    // 高亮当前页面导航链接
    const navLinks = document.querySelectorAll(`.nav-link[data-page="${currentPage}"], .mobile-nav-link[data-page="${currentPage}"]`);
    navLinks.forEach(link => {
        link.classList.add('active');
    });
    
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
    
    // 滚动时导航栏样式变化（加深阴影）
    window.addEventListener('scroll', function() {
        const nav = document.querySelector('nav');
        if (nav) {
            if (window.scrollY > 10) {
                nav.classList.add('shadow-lg');
            } else {
                nav.classList.remove('shadow-lg');
            }
        }
    });
});
</script>