<!-- 统一导航栏组件 -->
<nav class="bg-white shadow-md sticky top-0 left-0 right-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4">
        <div class="flex justify-between h-16">
            <!-- 左侧Logo/标题 -->
            <div class="flex items-center">
                <a href="main-plan.html" class="flex items-center">
                    <i class="fa fa-apple text-secondary text-2xl mr-2"></i>
                    <span class="font-bold text-xl text-dark">生活NOTE</span>
                </a>
            </div>

            <!-- 右侧导航链接 - 桌面端 -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="main-plan.html" class="nav-link" data-page="main-plan">
                    <i class="fa fa-home mr-2"></i>主计划
                </a>
                <a href="fruit-plan.html" class="nav-link" data-page="fruit-plan">
                    <i class="fa fa-apple mr-2"></i>水果计划
                </a>
                <a href="exercise-plan.html" class="nav-link" data-page="exercise-plan">
                    <i class="fa fa-heartbeat mr-2"></i>锻炼计划
                </a>
                <a href="expense-tracker.html" class="nav-link" data-page="expense-tracker">
                    <i class="fa fa-money mr-2"></i>支出记录
                </a>
            </div>

            <!-- 移动端菜单按钮 -->
            <div class="md:hidden flex items-center">
                <button id="mobile-menu-button" class="text-gray-600 hover:text-primary focus:outline-none">
                    <i class="fa fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg w-full">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="main-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="main-plan">
                <i class="fa fa-home mr-2"></i>主计划
            </a>
            <a href="fruit-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="fruit-plan">
                <i class="fa fa-apple mr-2"></i>水果计划
            </a>
            <a href="exercise-plan.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="exercise-plan">
                <i class="fa fa-heartbeat mr-2"></i>锻炼计划
            </a>
            <a href="expense-tracker.html" class="mobile-nav-link block px-3 py-2 rounded-md" data-page="expense-tracker">
                <i class="fa fa-money mr-2"></i>支出记录
            </a>
        </div>
    </div>
</nav>

<style>
/* 导航链接样式 */
.nav-link {
    color: #6B7280;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 0.5rem 0;
    position: relative;
}

.nav-link:hover {
    color: #F59E0B; /* accent color */
}

.nav-link.active {
    color: #F59E0B; /* accent color */
    font-weight: 600;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #F59E0B;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    transform: scaleX(1);
}

/* 移动端导航链接 */
.mobile-nav-link {
    color: #6B7280;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.mobile-nav-link:hover {
    color: #3B82F6;
    background-color: #F3F4F6;
}

.mobile-nav-link.active {
    color: #3B82F6;
    background-color: rgba(59, 130, 246, 0.1);
    font-weight: 600;
}

/* 关键：为所有页面添加顶部间距，避免内容被导航栏遮挡 */
body {
    margin: 0;
    padding-top: 4rem; /* 与导航栏高度一致 */
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前页面名称（用于高亮当前导航项）
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    // 高亮当前页面导航链接
    const navLinks = document.querySelectorAll(`.nav-link[data-page="${currentPage}"], .mobile-nav-link[data-page="${currentPage}"]`);
    navLinks.forEach(link => {
        link.classList.add('active');
    });
    
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
    
    // 滚动时导航栏样式变化（加深阴影）
    window.addEventListener('scroll', function() {
        const nav = document.querySelector('nav');
        if (nav) {
            if (window.scrollY > 10) {
                nav.classList.add('shadow-lg');
            } else {
                nav.classList.remove('shadow-lg');
            }
        }
    });
});
</script>