<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每周水果计划 - 生活NOTE</title>
    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="common-styles.css">
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入通用工具 -->
    <script src="common-utils.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#64B5F6',
                        secondary: '#81C784',
                        accent: '#FFB74D',
                        light: '#F5F7FA',
                        dark: '#37474F',
                        success: '#4CAF50'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .card-zoom {
                @apply transition-all duration-300 hover:scale-[1.02] hover:shadow-xl;
            }
            .nav-highlight {
                @apply relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-accent after:transform after:scale-x-0 hover:after:scale-x-100 after:transition-transform after:duration-300;
            }
            .nav-active {
                @apply text-accent after:scale-x-100;
            }
            .fruit-icon {
                @apply w-12 h-12 rounded-full flex items-center justify-center text-xl text-white mb-3;
            }
            .editable {
                @apply outline-none transition-colors duration-200 hover:bg-blue-50 rounded-md px-1 cursor-text;
            }
            .completed {
                @apply opacity-70 relative pl-6;
            }
            .completed::before {
                content: "✓";
                color: #4CAF50;
                font-weight: bold;
                position: absolute;
                left: 0;
            }
            .comment-item {
                @apply p-4 bg-white rounded-lg shadow-sm mb-4 border border-gray-100 transition-all duration-200 hover:shadow-md;
            }
            .toast {
                @apply fixed bottom-6 right-6 bg-dark text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center z-50;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light font-sans text-dark">
    <!-- 引入统一导航栏 -->
    <div id="navbar-container"></div>

    <main class="container mx-auto px-4 py-8" style="padding-top: 5rem;">
        <!-- 页面标题区 -->
        <div class="text-center mb-12">
            <h1 class="text-[clamp(1.8rem,5vw,2.8rem)] font-bold text-dark mb-3">每周水果营养计划</h1>
            <p class="text-gray-600 max-w-2xl mx-auto text-lg">可编辑计划内容，每日打卡记录，轻松培养健康习惯</p>
            <div class="flex items-center justify-center mt-4 text-sm text-gray-500">
                <i class="fa fa-calendar-o mr-2"></i>
                <span id="current-week">加载周信息中...</span>
            </div>
            <div class="w-24 h-1 bg-secondary mx-auto mt-5 rounded-full"></div>
        </div>
        
        <!-- 进度概览 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-10 transform transition-all duration-300 hover:shadow-lg">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold flex items-center">
                    <i class="fa fa-tasks text-primary mr-2"></i>本周完成进度
                </h2>
                <span id="progress-percentage" class="text-primary font-semibold">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-6 overflow-hidden">
                <div id="progress-bar" class="bg-success h-2.5 rounded-full transition-all duration-500 ease-out" style="width: 0%"></div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="flex items-center p-4 bg-primary/5 rounded-lg transform transition-all duration-300 hover:bg-primary/10">
                    <div class="w-12 h-12 rounded-full bg-primary flex items-center justify-center text-white mr-4">
                        <i class="fa fa-cutlery text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium">每日水果量</h3>
                        <p class="text-2xl font-bold text-primary">200-350g</p>
                    </div>
                </div>
                <div class="flex items-center p-4 bg-secondary/5 rounded-lg transform transition-all duration-300 hover:bg-secondary/10">
                    <div class="w-12 h-12 rounded-full bg-secondary flex items-center justify-center text-white mr-4">
                        <i class="fa fa-check-square-o text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium">已完成天数</h3>
                        <p id="completed-days" class="text-2xl font-bold text-secondary">0/7</p>
                    </div>
                </div>
                <div class="flex items-center p-4 bg-accent/5 rounded-lg transform transition-all duration-300 hover:bg-accent/10">
                    <div class="w-12 h-12 rounded-full bg-accent flex items-center justify-center text-white mr-4">
                        <i class="fa fa-sun-o text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-medium">维生素目标</h3>
                        <p class="text-2xl font-bold text-accent">100%</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 每日水果计划卡片 - 可编辑版本 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- 周一 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="monday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-sun-o mr-2"></i> 周一
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-red-100 text-red-500">
                        <i class="fa fa-apple"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-monday" contenteditable="true">苹果 + 蓝莓</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-monday" contenteditable="true">提供丰富的膳食纤维和抗氧化物质</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-monday" contenteditable="true">建议：早餐后1小时食用</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="monday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周二 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="tuesday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-cloud mr-2"></i> 周二
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-yellow-100 text-yellow-500">
                        <i class="fa fa-ban"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-tuesday" contenteditable="true">香蕉 + 猕猴桃</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-tuesday" contenteditable="true">补充钾元素和维生素C，增强免疫力</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-tuesday" contenteditable="true">建议：运动后食用</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="tuesday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周三 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="wednesday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-sun-o mr-2"></i> 周三
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-orange-100 text-orange-500">
                        <i class="fa fa-cutlery"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-wednesday" contenteditable="true">橙子 + 草莓</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-wednesday" contenteditable="true">丰富的维生素C来源，促进铁吸收</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-wednesday" contenteditable="true">建议：下午茶时间</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="wednesday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周四 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="thursday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-cloud mr-2"></i> 周四
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-purple-100 text-purple-500">
                        <i class="fa fa-grape"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-thursday" contenteditable="true">葡萄 + 梨</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-thursday" contenteditable="true">润肺生津，适合干燥季节食用</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-thursday" contenteditable="true">建议：晚餐后1小时</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="thursday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周五 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="friday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-sun-o mr-2"></i> 周五
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-green-100 text-green-500">
                        <i class="fa fa-leaf"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-friday" contenteditable="true">西瓜 + 樱桃</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-friday" contenteditable="true">补水佳品，富含番茄红素和铁元素</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-friday" contenteditable="true">建议：午餐后食用</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="friday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周六 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="saturday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-moon-o mr-2"></i> 周六
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-orange-100 text-orange-500">
                        <i class="fa fa-star-o"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-saturday" contenteditable="true">芒果 + 圣女果</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-saturday" contenteditable="true">丰富的胡萝卜素，有益视力健康</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-saturday" contenteditable="true">建议：上午加餐时间</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="saturday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 周日 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden card-zoom" data-day="sunday">
                <div class="bg-primary text-white p-4 flex justify-between items-center">
                    <h3 class="font-bold text-lg flex items-center">
                        <i class="fa fa-moon-o mr-2"></i> 周日
                    </h3>
                    <span class="day-status hidden text-xs bg-success text-white px-2 py-1 rounded-full animate-fadeIn">
                        已完成
                    </span>
                </div>
                <div class="p-5">
                    <div class="fruit-icon bg-yellow-100 text-yellow-500">
                        <i class="fa fa-lemon-o"></i>
                    </div>
                    <h4 class="font-semibold text-lg mb-1 editable" data-field="title-sunday" contenteditable="true">菠萝 + 火龙果</h4>
                    <p class="text-gray-600 text-sm mb-3 editable" data-field="description-sunday" contenteditable="true">促进消化，富含膳食纤维</p>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <i class="fa fa-clock-o mr-1"></i>
                        <span class="editable" data-field="time-sunday" contenteditable="true">建议：早餐时搭配食用</span>
                    </div>
                    
                    <!-- 打卡区域 -->
                    <div class="border-t pt-4">
                        <div class="flex items-center justify-between mb-3">
                            <button class="check-in-btn bg-gray-100 hover:bg-success hover:text-white text-gray-700 px-3 py-1 rounded-md text-sm transition-colors duration-200 flex items-center">
                                <i class="fa fa-check mr-1"></i> 完成打卡
                            </button>
                            <button class="reset-btn hidden bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs transition-colors duration-200">
                                <i class="fa fa-refresh"></i>
                            </button>
                        </div>
                        
                        <!-- 快速评论输入 -->
                        <div class="comment-input hidden transition-all duration-300 transform scale-95 opacity-0" style="transform-origin: top;">
                            <textarea placeholder="添加今日感想..." class="w-full border border-gray-200 rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-primary" rows="2" data-day="sunday"></textarea>
                            <button class="save-comment-btn text-xs bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors">保存评论</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 水果营养知识卡片 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-12 transform transition-all duration-300 hover:shadow-lg">
            <h2 class="text-xl font-semibold mb-5 flex items-center">
                <i class="fa fa-lightbulb-o text-accent mr-2"></i>水果营养小知识
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="mr-4 text-accent text-xl mt-1">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="font-medium mb-1 editable" data-field="tip1-title" contenteditable="true">多样搭配原则</h3>
                        <p class="text-gray-600 text-sm editable" data-field="tip1-desc" contenteditable="true">不同颜色的水果含有不同的植物营养素，多样化选择能获得更全面的营养。</p>
                    </div>
                </div>
                <div class="flex p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="mr-4 text-accent text-xl mt-1">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="font-medium mb-1 editable" data-field="tip2-title" contenteditable="true">适量食用</h3>
                        <p class="text-gray-600 text-sm editable" data-field="tip2-desc" contenteditable="true">水果虽好但需适量，过量摄入果糖可能导致热量超标。</p>
                    </div>
                </div>
                <div class="flex p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="mr-4 text-accent text-xl mt-1">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="font-medium mb-1 editable" data-field="tip3-title" contenteditable="true">食用时间</h3>
                        <p class="text-gray-600 text-sm editable" data-field="tip3-desc" contenteditable="true">餐前1小时或餐后2小时食用水果，更有利于营养吸收。</p>
                    </div>
                </div>
                <div class="flex p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="mr-4 text-accent text-xl mt-1">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <div>
                        <h3 class="font-medium mb-1 editable" data-field="tip4-title" contenteditable="true">新鲜优先</h3>
                        <p class="text-gray-600 text-sm editable" data-field="tip4-desc" contenteditable="true">优先选择新鲜当季水果，尽量减少加工水果制品的摄入。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 评论区 - 放在页面底部 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-12 transform transition-all duration-300 hover:shadow-lg">
            <h2 class="text-xl font-semibold mb-5 flex items-center">
                <i class="fa fa-comments text-primary mr-2"></i>本周评论记录
            </h2>
            <div id="comments-container" class="space-y-4">
                <!-- 评论会通过JavaScript动态添加到这里 -->
                <div class="text-center text-gray-500 py-8" id="no-comments-message">
                    <i class="fa fa-comment-o text-3xl mb-2 opacity-30"></i>
                    <p>还没有评论，完成打卡后可以添加你的感想哦</p>
                </div>
            </div>
        </div>
    </main>
    
    <footer class="bg-dark text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <div class="flex justify-center items-center mb-4">
                <i class="fa fa-apple text-secondary text-2xl mr-2"></i>
                <span class="font-bold text-xl">健康生活计划</span>
            </div>
            <p class="text-gray-400 text-sm max-w-md mx-auto editable" data-field="footer-text" contenteditable="true">
                本计划仅供参考，具体水果选择可根据个人体质和季节进行调整
            </p>
            <div class="mt-6 text-gray-400 text-sm">
                &copy; 2023 健康生活计划 | 让每一天都充满活力
            </div>
        </div>
    </footer>

    <!-- 通知提示组件 -->
    <div id="toast" class="toast">
        <i id="toast-icon" class="fa fa-check-circle mr-2"></i>
        <span id="toast-message"></span>
    </div>

    <script>
        // 使用类封装功能，提高代码组织性
        class FruitPlan {
            constructor() {
                // 初始化DOM元素引用
                this.elements = {
                    menuToggle: document.getElementById('menu-toggle'),
                    mobileMenu: document.getElementById('mobile-menu'),
                    currentWeek: document.getElementById('current-week'),
                    progressBar: document.getElementById('progress-bar'),
                    progressPercentage: document.getElementById('progress-percentage'),
                    completedDays: document.getElementById('completed-days'),
                    commentsContainer: document.getElementById('comments-container'),
                    noCommentsMessage: document.getElementById('no-comments-message'),
                    toast: document.getElementById('toast'),
                    toastIcon: document.getElementById('toast-icon'),
                    toastMessage: document.getElementById('toast-message')
                };
                
                // 初始化应用
                this.init();
            }
            
            // 初始化函数
            init() {
                // 初始化周信息
                const currentWeekInfo = this.getCurrentWeekInfo();
                this.checkWeekReset(currentWeekInfo);
                
                // 加载保存的数据
                this.loadSavedData();
                
                // 绑定事件处理函数
                this.bindEvents();
            }
            
            // 获取当前周信息并显示
            getCurrentWeekInfo() {
                const now = new Date();
                const firstDayOfWeek = new Date(now);
                firstDayOfWeek.setDate(now.getDate() - now.getDay() + 1); // 周一为一周的第一天
                
                const lastDayOfWeek = new Date(firstDayOfWeek);
                lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);
                
                // 格式化日期
                const formatDate = (date) => {
                    return `${date.getMonth() + 1}月${date.getDate()}日`;
                };
                
                const weekInfo = `本周: ${formatDate(firstDayOfWeek)} - ${formatDate(lastDayOfWeek)}`;
                this.elements.currentWeek.textContent = weekInfo;
                
                return {
                    year: now.getFullYear(),
                    week: this.getWeekNumber(now)
                };
            }
            
            // 获取当前是当年的第几周
            getWeekNumber(date) {
                const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
                const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
                return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
            }
            
            // 检查是否需要重置每周数据
            checkWeekReset(currentWeekInfo) {
                try {
                    const savedWeekInfo = JSON.parse(localStorage.getItem('currentWeekInfo') || '{"year": 0, "week": 0}');
                    
                    // 如果是新的一周，重置数据
                    if (currentWeekInfo.year !== savedWeekInfo.year || currentWeekInfo.week !== savedWeekInfo.week) {
                        this.resetWeekData();
                        localStorage.setItem('currentWeekInfo', JSON.stringify(currentWeekInfo));
                        this.showToast('已自动更新为新一周的水果计划', 'info');
                    }
                } catch (error) {
                    console.error('处理周信息时出错:', error);
                    this.showToast('加载数据时出错', 'error');
                }
            }
            
            // 重置每周数据
            resetWeekData() {
                try {
                    const savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    // 保留编辑的内容，但重置打卡状态和评论
                    if (savedData.checkIns) {
                        savedData.checkIns = {};
                    }
                    if (savedData.comments) {
                        savedData.comments = {};
                    }
                    
                    localStorage.setItem('fruitPlanData', JSON.stringify(savedData));
                } catch (error) {
                    console.error('重置周数据时出错:', error);
                    this.showToast('重置数据时出错', 'error');
                }
            }
            
            // 绑定所有事件处理函数
            bindEvents() {
                // 移动端菜单切换
                this.elements.menuToggle.addEventListener('click', () => {
                    this.elements.mobileMenu.classList.toggle('hidden');
                });
                
                // 为可编辑元素添加保存功能
                document.querySelectorAll('.editable').forEach(element => {
                    // 失去焦点时保存
                    element.addEventListener('blur', () => {
                        this.saveEditableContent(element);
                    });
                    
                    // 按Enter键时也保存并失去焦点（仅适用于非文本区域）
                    if (element.tagName !== 'TEXTAREA') {
                        element.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                element.blur();
                            }
                        });
                    }
                });
                
                // 打卡按钮功能
                document.querySelectorAll('.check-in-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const card = button.closest('[data-day]');
                        const day = card.dataset.day;
                        this.handleCheckIn(card, day);
                    });
                });
                
                // 重置打卡按钮
                document.querySelectorAll('.reset-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const card = button.closest('[data-day]');
                        const day = card.dataset.day;
                        this.handleResetCheckIn(card, day);
                    });
                });
                
                // 保存评论
                document.querySelectorAll('.save-comment-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const card = button.closest('[data-day]');
                        const day = card.dataset.day;
                        const commentTextarea = card.querySelector(`textarea[data-day="${day}"]`);
                        const comment = commentTextarea.value.trim();
                        
                        this.saveComment(day, comment);
                        this.renderComments();
                        
                        // 显示保存成功提示
                        this.showToast('评论已保存', 'success');
                    });
                });
                
                // 滚动时改变导航栏样式
                window.addEventListener('scroll', () => {
                    const header = document.querySelector('header');
                    if (window.scrollY > 10) {
                        header.classList.add('shadow');
                        header.classList.remove('shadow-sm');
                    } else {
                        header.classList.remove('shadow');
                        header.classList.add('shadow-sm');
                    }
                });
            }
            
            // 处理打卡操作
            handleCheckIn(card, day) {
                const checkInBtn = card.querySelector('.check-in-btn');
                const resetBtn = card.querySelector('.reset-btn');
                const commentInput = card.querySelector('.comment-input');
                const statusBadge = card.querySelector('.day-status');
                const titleElement = card.querySelector(`[data-field="title-${day}"]`);
                
                // 切换打卡状态
                checkInBtn.classList.add('hidden');
                resetBtn.classList.remove('hidden');
                statusBadge.classList.remove('hidden');
                titleElement.classList.add('completed');
                
                // 显示评论输入框并添加动画
                commentInput.classList.remove('hidden');
                setTimeout(() => {
                    commentInput.classList.remove('scale-95', 'opacity-0');
                }, 50);
                
                // 保存打卡状态
                this.saveCheckInStatus(day, true);
                
                // 更新进度
                this.updateProgress();
                
                // 显示提示
                this.showToast('已完成今日水果计划', 'success');
            }
            
            // 处理重置打卡操作
            handleResetCheckIn(card, day) {
                const checkInBtn = card.querySelector('.check-in-btn');
                const resetBtn = card.querySelector('.reset-btn');
                const commentInput = card.querySelector('.comment-input');
                const statusBadge = card.querySelector('.day-status');
                const titleElement = card.querySelector(`[data-field="title-${day}"]`);
                const commentTextarea = card.querySelector(`.comment-input textarea[data-day="${day}"]`);
                
                // 添加收起动画
                commentInput.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    commentInput.classList.add('hidden');
                }, 300);
                
                // 重置状态
                resetBtn.classList.add('hidden');
                checkInBtn.classList.remove('hidden');
                statusBadge.classList.add('hidden');
                titleElement.classList.remove('completed');
                
                // 保存重置状态
                this.saveCheckInStatus(day, false);
                this.saveComment(day, '');
                
                // 更新进度和评论区
                this.updateProgress();
                this.renderComments();
                
                // 显示提示
                this.showToast('已取消今日打卡', 'info');
            }
            
            // 保存可编辑内容到本地存储
            saveEditableContent(element) {
                try {
                    const field = element.dataset.field;
                    const content = element.innerHTML;
                    
                    // 获取现有数据或创建新对象
                    let savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    // 保存内容
                    savedData[field] = content;
                    
                    // 存储回本地存储
                    localStorage.setItem('fruitPlanData', JSON.stringify(savedData));
                    
                    // 显示保存提示
                    this.showToast('内容已保存', 'success');
                } catch (error) {
                    console.error('保存编辑内容时出错:', error);
                    this.showToast('保存失败', 'error');
                }
            }
            
            // 保存打卡状态
            saveCheckInStatus(day, completed) {
                try {
                    let savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    if (!savedData.checkIns) {
                        savedData.checkIns = {};
                    }
                    
                    savedData.checkIns[day] = completed;
                    
                    localStorage.setItem('fruitPlanData', JSON.stringify(savedData));
                } catch (error) {
                    console.error('保存打卡状态时出错:', error);
                }
            }
            
            // 保存评论
            saveComment(day, comment) {
                try {
                    let savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    if (!savedData.comments) {
                        savedData.comments = {};
                    }
                    
                    // 保存评论时带上时间戳
                    if (comment) {
                        savedData.comments[day] = {
                            content: comment,
                            timestamp: new Date().toISOString()
                        };
                    } else {
                        // 如果评论为空，则删除该评论
                        delete savedData.comments[day];
                    }
                    
                    localStorage.setItem('fruitPlanData', JSON.stringify(savedData));
                } catch (error) {
                    console.error('保存评论时出错:', error);
                    this.showToast('保存评论失败', 'error');
                }
            }
            
            // 加载保存的数据
            loadSavedData() {
                try {
                    const savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    // 加载可编辑内容
                    document.querySelectorAll('.editable').forEach(element => {
                        const field = element.dataset.field;
                        if (savedData[field]) {
                            element.innerHTML = savedData[field];
                        }
                    });
                    
                    // 加载打卡状态
                    if (savedData.checkIns) {
                        Object.keys(savedData.checkIns).forEach(day => {
                            if (savedData.checkIns[day]) {
                                const card = document.querySelector(`[data-day="${day}"]`);
                                if (card) {
                                    const checkInBtn = card.querySelector('.check-in-btn');
                                    const resetBtn = card.querySelector('.reset-btn');
                                    const commentInput = card.querySelector('.comment-input');
                                    const statusBadge = card.querySelector('.day-status');
                                    const titleElement = card.querySelector(`[data-field="title-${day}"]`);
                                    const commentTextarea = card.querySelector(`.comment-input textarea[data-day="${day}"]`);
                                    
                                    checkInBtn.classList.add('hidden');
                                    resetBtn.classList.remove('hidden');
                                    statusBadge.classList.remove('hidden');
                                    titleElement.classList.add('completed');
                                    commentInput.classList.remove('hidden', 'scale-95', 'opacity-0');
                                    
                                    // 加载评论
                                    if (savedData.comments && savedData.comments[day]) {
                                        commentTextarea.value = savedData.comments[day].content;
                                    }
                                }
                            }
                        });
                    }
                    
                    // 渲染评论区
                    this.renderComments();
                    
                    // 更新进度
                    this.updateProgress();
                } catch (error) {
                    console.error('加载保存数据时出错:', error);
                    this.showToast('加载数据时出错', 'error');
                }
            }
            
            // 渲染评论区
            renderComments() {
                try {
                    const savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    
                    // 清空现有评论（保留无评论提示）
                    Array.from(this.elements.commentsContainer.children).forEach(child => {
                        if (child.id !== 'no-comments-message') {
                            child.remove();
                        }
                    });
                    
                    // 检查是否有评论
                    if (!savedData.comments || Object.keys(savedData.comments).length === 0) {
                        this.elements.noCommentsMessage.classList.remove('hidden');
                        return;
                    }
                    
                    // 隐藏无评论提示
                    this.elements.noCommentsMessage.classList.add('hidden');
                    
                    // 星期名称映射
                    const dayNames = {
                        'monday': '周一',
                        'tuesday': '周二',
                        'wednesday': '周三',
                        'thursday': '周四',
                        'friday': '周五',
                        'saturday': '周六',
                        'sunday': '周日'
                    };
                    
                    // 转换为数组并按时间排序
                    let commentsArray = [];
                    Object.keys(savedData.comments).forEach(day => {
                        if (savedData.comments[day] && savedData.comments[day].content) {
                            commentsArray.push({
                                day: day,
                                dayName: dayNames[day],
                                ...savedData.comments[day]
                            });
                        }
                    });
                    
                    // 按时间排序（最新的在前面）
                    commentsArray.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    
                    // 添加评论到容器
                    commentsArray.forEach(comment => {
                        const commentElement = document.createElement('div');
                        commentElement.className = 'comment-item';
                        
                        // 格式化日期
                        const date = new Date(comment.timestamp);
                        const formattedDate = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
                        
                        commentElement.innerHTML = `
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium text-primary">${comment.dayName}</h4>
                                <span class="text-xs text-gray-500">${formattedDate}</span>
                            </div>
                            <p class="text-gray-700 text-sm">${comment.content}</p>
                        `;
                        
                        // 添加淡入动画
                        commentElement.style.opacity = '0';
                        commentElement.style.transform = 'translateY(10px)';
                        commentElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        
                        this.elements.commentsContainer.appendChild(commentElement);
                        
                        // 触发动画
                        setTimeout(() => {
                            commentElement.style.opacity = '1';
                            commentElement.style.transform = 'translateY(0)';
                        }, 50);
                    });
                } catch (error) {
                    console.error('渲染评论时出错:', error);
                }
            }
            
            // 更新进度条和完成天数
            updateProgress() {
                try {
                    const savedData = JSON.parse(localStorage.getItem('fruitPlanData') || '{}');
                    const checkIns = savedData.checkIns || {};
                    const totalDays = 7;
                    let completedDays = 0;
                    
                    // 计算已完成天数
                    Object.values(checkIns).forEach(completed => {
                        if (completed) completedDays++;
                    });
                    
                    // 更新进度条
                    const progressPercentage = Math.round((completedDays / totalDays) * 100);
                    this.elements.progressBar.style.width = `${progressPercentage}%`;
                    this.elements.progressPercentage.textContent = `${progressPercentage}%`;
                    this.elements.completedDays.textContent = `${completedDays}/${totalDays}`;
                } catch (error) {
                    console.error('更新进度时出错:', error);
                }
            }
            
            // 显示提示消息
            showToast(message, type = 'info') {
                const { toast, toastIcon, toastMessage } = this.elements;
                
                toastMessage.textContent = message;
                
                // 设置图标和颜色
                if (type === 'success') {
                    toastIcon.className = 'fa fa-check-circle mr-2';
                    toast.classList.remove('bg-danger', 'bg-primary');
                    toast.classList.add('bg-success');
                } else if (type === 'error') {
                    toastIcon.className = 'fa fa-exclamation-circle mr-2';
                    toast.classList.remove('bg-success', 'bg-primary');
                    toast.classList.add('bg-danger');
                } else {
                    toastIcon.className = 'fa fa-info-circle mr-2';
                    toast.classList.remove('bg-danger', 'bg-success');
                    toast.classList.add('bg-primary');
                }
                
                // 显示提示
                toast.classList.remove('translate-y-20', 'opacity-0');
                toast.classList.add('translate-y-0', 'opacity-100');
                
                // 3秒后隐藏
                setTimeout(() => {
                    toast.classList.remove('translate-y-0', 'opacity-100');
                    toast.classList.add('translate-y-20', 'opacity-0');
                }, 3000);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            // 创建水果计划实例
            const fruitPlan = new FruitPlan();
        });
    </script>

    <!-- 加载统一导航栏 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载导航栏
            fetch('navbar.html')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('navbar-container').innerHTML = html;

                    // 执行导航栏中的脚本
                    const scripts = document.getElementById('navbar-container').querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                    });
                })
                .catch(error => {
                    console.error('加载导航栏失败:', error);
                });
        });
    </script>
</body>
</html>
